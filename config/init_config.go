package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置信息
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitTaskCfg", cmodel.InitTaskCfg)                       // 初始化任务配置
	serviceConfig.Register("InitTaskRewardCfg", cmodel.InitTaskRewardCfg)           // 初始化任务奖励配置
	serviceConfig.Register("InitTaskCondCfg", cmodel.InitTaskCondCfg)               // 初始化任务条件配置
	serviceConfig.Register("InitTaskCondGroupCfg", cmodel.InitTaskCondGroupCfg)     // 初始化任务条件组配置
	serviceConfig.Register("InitTaskProgressCfg", cmodel.InitTaskProgressCfg)       // 初始化任务进度配置
	serviceConfig.Register("InitTaskConstCfg", cmodel.InitTaskConstCfg)             // 初始化任务常量配置
	serviceConfig.Register("InitTaskOpenRuleCfg", cmodel.InitTaskOpenRuleCfg)       // 初始化任务开放规则配置
	serviceConfig.Register("InitTaskAchieveTypeCfg", cmodel.InitTaskAchieveTypeCfg) // 初始化任务成就类型配置
	serviceConfig.Register("InitTaskGroupCfg", cmodel.InitTaskGroupCfg)             // 初始化任务组配置
	serviceConfig.Register("InitFeatureHideCfg", cmodel.InitFeatureHideCfg)         // 初始化功能开关

	return serviceConfig.ExecuteAll()
}
