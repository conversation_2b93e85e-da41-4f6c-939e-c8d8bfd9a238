package config

import (
	"fmt"
	"time"
)

// AchieveRedDotID
// 查询成就红点的配置id role_info.xlsx(FeatureHide)
const AchieveRedDotID = 13

const (
	RedisKeyLockTask         = "task:lock:%d"
	RedisKeyLockTaskPond     = "task:pond:lock:%d"
	RedisKeyLockTaskProgress = "task:progress:lock:%d"
	RedisKeyLockQuest        = "quest:lock:%d"
)

const (
	// task:cache:$playerId:$category
	RedisKeyCacheTask = "task:cache:%d:%d"
	// RedisKeyCacheTaskPond     = "task:pond:cache:%d"

	// task:progress:$playerId:$category
	RedisKeyCacheTaskProgress = "task:progress:%d:%d"

	// quest:$questMode:$playerId:$date
	RedisKeyCacheQuest = "quest:%d:%d:%s"
)

const (
	TaskCacheExpire  = 3 * 86400 * time.Second
	QuestCacheExpire = 48 * time.Hour // quest的48小时过期时间
)

// 任务修改分布式锁
func DLMTaskLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockTask, playerId)
}

// 进度奖励
func DLMTaskProgressLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockTaskProgress, playerId)
}

// quest任务分布式锁
func DLMQuestLockKey(playerId uint64) string {
	return fmt.Sprintf(RedisKeyLockQuest, playerId)
}
