# server
rpc_server_name: task
rpc_port:  12501

# 端口号
http_port: 22501

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/task
log_json: false
log_kafka_enable: true

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  lock:
    addr: 192.168.1.58:6379
    passwd: 8888
  task:
    addr: 192.168.1.58:6379
    passwd: 8888


mysql_list:
  fancy_task:
    user: root
    addr: 192.168.1.58:3306
    passwd: fancydb2024#
    db: fancy_task

consul_addr: 192.168.1.58:8500

nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

rpc_server_tags: debug


kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10
