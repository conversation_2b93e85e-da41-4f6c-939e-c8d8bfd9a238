package logic_quest

import (
	"context"
	"fmt"
	"tasksrv/config"
	dao_quest "tasksrv/internal/dao/quest"
	"tasksrv/internal/logic/quest_dispose"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
)

// GetQuestList 获取任务列表
func GetQuestList(ctx context.Context, playerId uint64, req *taskPB.GetQuestListReq) (*taskPB.GetQuestListRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("GetQuestList playerId:%d, req:%+v", playerId, req)

	rsp := &taskPB.GetQuestListRsp{
		Ret: protox.DefaultResult(),
	}

	// 获取任务处理器
	questDispose, err := quest_dispose.GetFactory().GetQuestDispose(req.QuestMode)
	if err != nil {
		entry.Errorf("get quest dispose failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
	}

	// 使用model层构建方法创建请求对象
	questListReq := model.NewQuestListRequest(playerId, req)

	// 使用分布式锁
	unlock := dlm.LockKey(config.DLMQuestLockKey(playerId))
	defer unlock()

	// 委托给具体的任务处理器处理
	response, err := questDispose.GetQuestList(ctx, questListReq)
	if err != nil {
		entry.Errorf("dispose get quest list failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, err.Error())
	}

	// 转换为Proto格式
	rsp.CurrentList = make([]*commonPB.QuestList, len(response.CurrentList))
	for i, quest := range response.CurrentList {
		rsp.CurrentList[i] = quest.ToProto()
	}

	rsp.CompletedList = make([]*commonPB.QuestList, len(response.CompletedList))
	for i, quest := range response.CompletedList {
		rsp.CompletedList[i] = quest.ToProto()
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Infof("GetQuestList success, current:%d, completed:%d", len(rsp.CurrentList), len(rsp.CompletedList))
	return rsp, nil
}

// SubmitQuest 提交任务
func SubmitQuest(ctx context.Context, playerId uint64, req *taskPB.SubmitQuestReq) (*taskPB.SubmitQuestRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("SubmitQuest playerId:%d, req:%+v", playerId, req)

	// 获取任务处理器
	questDispose, err := quest_dispose.GetFactory().GetQuestDispose(req.QuestMode)
	if err != nil {
		entry.Errorf("get quest dispose failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
	}

	questModeCfg := cmodel.GetQuestMode(int64(req.QuestMode), consul_config.WithGrpcCtx(ctx))
	if questModeCfg == nil {
		return nil, fmt.Errorf("quest mode cfg cannot be nil")
	}
	if questModeCfg.IsOpen == false {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "quest mode not open")
	}

	questPoolCfg := cmodel.GetQuestPool(req.QuestId, consul_config.WithGrpcCtx(ctx))
	if questPoolCfg == nil {
		return nil, fmt.Errorf("quest pool cfg cannot be nil")
	}
	questCondCfg := cmodel.GetQuestCond(questPoolCfg.Cond, consul_config.WithGrpcCtx(ctx))
	if questCondCfg == nil {
		return nil, fmt.Errorf("quest condition cannot be nil")
	}
	questRewardCfg := cmodel.GetQuestReward(questPoolCfg.Reward, consul_config.WithGrpcCtx(ctx))
	if questRewardCfg == nil {
		return nil, fmt.Errorf("quest reward cfg cannot be nil")
	}

	unlock := dlm.LockKey(config.DLMQuestLockKey(playerId))
	defer unlock()

	// 获取任务信息
	currentDate := model.GetCurrentDate()
	questData, err := dao_quest.GetPlayerQuestData(ctx, req.QuestMode, playerId, currentDate)
	if err != nil {
		entry.Errorf("get player quest data failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	if len(questData.CompletedList) >= int(questModeCfg.DailyLimit) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "daily limit reached")
	}

	// 校验是否为当前任务
	isCur := false
	for _, v := range questData.CurrentList[req.PondId] {
		if v.QuestId == req.QuestId {
			isCur = true
			break
		}
	}
	if !isCur {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "quest not found")
	}

	// 验证和提交任务
	validationCtx := model.NewQuestValidationContext(playerId, req, questCondCfg)
	err = questDispose.ValidateAndSubmit(ctx, validationCtx, questData)
	if err != nil {
		entry.Errorf("validate and submit failed: %+v", err)
		return nil, err
	}

	// 保存更新后的数据
	err = dao_quest.SavePlayerQuestData(ctx, req.QuestMode, playerId, currentDate, questData)
	if err != nil {
		entry.Errorf("save player quest data failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	// 处理奖励
	rewardCtx := model.NewQuestRewardContext(playerId, req.QuestId, req.QuestMode, questRewardCfg)
	rewards, err := questDispose.ProcessReward(ctx, rewardCtx)
	if err != nil {
		entry.Warnf("process reward failed: %+v", err)
		// 奖励失败不影响任务完成
	}

	// 执行后处理钩子
	hookCtx := model.NewQuestHookContext(playerId, req, questCondCfg, questRewardCfg)
	questDispose.PostSubmitHook(ctx, hookCtx)

	lastQuest := &commonPB.QuestList{}
	if questData.LastRefreshQuest != nil {
		lastQuest = questData.LastRefreshQuest.ToProto()
	}

	rsp := &taskPB.SubmitQuestRsp{
		Ret:       protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS),
		QuestInfo: lastQuest,
		Reward:    rewards,
	}

	entry.Infof("SubmitQuest success, playerId:%d, questId:%d", playerId, req.QuestId)
	return rsp, nil
}

// RefreshQuest 刷新任务
func RefreshQuest(ctx context.Context, playerId uint64, req *taskPB.RefreshQuestReq) (*taskPB.RefreshQuestRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("RefreshQuest playerId:%d, req:%+v", playerId, req)

	rsp := &taskPB.RefreshQuestRsp{
		Ret: protox.DefaultResult(),
	}

	// 获取任务模式配置
	questModeCfg := cmodel.GetQuestMode(int64(req.QuestMode), consul_config.WithGrpcCtx(ctx))
	if questModeCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "quest mode not found")
	}
	if questModeCfg.IsRefresh == int32(commonPB.EXPRESSION_TYPE_ET_NO) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "quest mode not open")
	}

	unlock := dlm.LockKey(config.DLMQuestLockKey(playerId))
	defer unlock()

	// 扣除刷新道具
	itemBase := &commonPB.ItemBase{
		ItemId:    questModeCfg.RefreshItem,
		ItemCount: questModeCfg.RefreshItemUse,
	}
	_, err := item_kit.DeductItem(ctx, playerId, []*commonPB.ItemBase{itemBase}, commonPB.ITEM_SOURCE_TYPE_IST_TASK_REWARD)
	if err != nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "deduct item failed")
	}

	// 获取当前任务数据
	currentDate := model.GetCurrentDate()
	questData, err := dao_quest.GetPlayerQuestData(ctx, req.QuestMode, playerId, currentDate)
	if err != nil {
		entry.Errorf("get player quest data failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	// 删除被刷新的任务
	for i, quest := range questData.CurrentList[req.PondId] {
		if quest.QuestId == req.QuestId {
			questData.CurrentList[req.PondId] = append(questData.CurrentList[req.PondId][:i], questData.CurrentList[req.PondId][i+1:]...)
			break
		}
	}

	// 刷新任务
	questDispose, err := quest_dispose.GetFactory().GetQuestDispose(req.QuestMode)
	if err != nil {
		entry.Errorf("get quest dispose failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
	}
	refreshReq := &model.QuestListRequest{
		PlayerId:  playerId,
		PondId:    req.PondId,
		QuestMode: req.QuestMode,
	}
	if err = questDispose.RefreshQuestList(ctx, refreshReq, questData); err != nil {
		entry.Errorf("refresh quest list failed: %+v", err)
		return nil, err
	}

	// 保存更新后的数据
	err = dao_quest.SavePlayerQuestData(ctx, req.QuestMode, playerId, currentDate, questData)
	if err != nil {
		entry.Errorf("save player quest data failed: %+v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	rsp.QuestInfo = questData.LastRefreshQuest.ToProto()
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Infof("RefreshQuest success, playerId:%d, questId:%d", playerId, req.QuestId)
	return rsp, nil
}
