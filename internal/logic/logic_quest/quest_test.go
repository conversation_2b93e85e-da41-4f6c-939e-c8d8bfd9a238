package logic_quest

import (
	"context"
	"tasksrv/internal/test"
	"testing"

	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
)

func TestMain(m *testing.M) {
	test.InitConsul()
	test.InitRedis()

	m.Run()
}

func TestGetQuestList(t *testing.T) {
	type args struct {
		ctx      context.Context
		playerId uint64
		req      *taskPB.GetQuestListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *taskPB.GetQuestListRsp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test.NewCtxWithPlayerId(827),
				playerId: 827,
				req: &taskPB.GetQuestListReq{
					PondId:    301020000,
					QuestMode: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetQuestList(tt.args.ctx, tt.args.playerId, tt.args.req)
			t.Log(err)
			t.Logf("========%+v", got)
		})
	}
}

func TestSubmitQuest(t *testing.T) {
	type args struct {
		ctx      context.Context
		playerId uint64
		req      *taskPB.SubmitQuestReq
	}
	tests := []struct {
		name    string
		args    args
		want    *taskPB.SubmitQuestRsp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:      test.NewCtxWithPlayerId(827),
				playerId: 827,
				req: &taskPB.SubmitQuestReq{
					QuestId:         1,
					PondId:          301020000,
					QuestMode:       1,
					SelectedFishIds: []string{"588a3a62-8707-11f0-82e1-0ac634529125"},
				},
			},
		}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := SubmitQuest(tt.args.ctx, tt.args.playerId, tt.args.req)
			t.Log(err)
			t.Logf("========%+v", got)
		})
	}
}
