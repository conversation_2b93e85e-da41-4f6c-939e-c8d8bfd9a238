package quest_dispose

import (
	"context"
	"fmt"
	"tasksrv/internal/model"
	"tasksrv/internal/repo/record"
	rpcSpot "tasksrv/internal/repo/rpc_spot"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

// QuestBountyImpl 悬赏委托实现 - 简化实现
type QuestBountyImpl struct {
	helper *QuestHelper
}

// NewQuestBountyImpl 创建悬赏委托处理器
func NewQuestBountyImpl() *QuestBountyImpl {
	return &QuestBountyImpl{
		helper: NewQuestHelper(commonPB.QuestMode_QR_BOUNTY),
	}
}

// GetQuestList 获取悬赏委托列表
func (q *QuestBountyImpl) GetQuestList(ctx context.Context, req *model.QuestListRequest) (*model.QuestListResponse, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("BountyImpl.GetQuestList playerId:%d, pondId:%d", req.PlayerId, req.PondId)

	questModeCfg := cmodel.GetQuestMode(int64(req.QuestMode), consul_config.WithGrpcCtx(ctx))
	if questModeCfg == nil {
		return nil, fmt.Errorf("quest mode cfg cannot be nil")
	}

	// 验证请求
	if err := q.helper.ValidateBasicRequest(ctx, req, questModeCfg); err != nil {
		entry.Errorf("validate request failed: %+v", err)
		return nil, err
	}

	// 获取玩家任务数据
	currentDate := model.GetCurrentDate()
	questData, err := q.helper.GetPlayerQuestData(ctx, req, currentDate)
	if err != nil {
		entry.Errorf("get player quest data failed: %+v", err)
		return nil, err
	}

	// 超过上限返回当前列表
	if len(questData.CompletedList) >= int(questModeCfg.DailyLimit) {

		return q.helper.BuildQuestListResponse(req.PondId, questData), nil
	}
	questData.DailyLimit = questModeCfg.DailyLimit

	// 判断是否需要刷新
	needRefreshNum := int(questModeCfg.DisplayCount) - (len(questData.CurrentList[req.PondId]))
	if needRefreshNum > 0 {
		err = q.RefreshQuestList(ctx, req, questData)
		if err != nil {
			entry.Errorf("refresh quest list failed: %+v", err)
			return nil, err
		}

		// 保存刷新后的数据
		err = q.helper.SavePlayerQuestData(ctx, req, currentDate, questData)
		if err != nil {
			entry.Errorf("save quest data failed: %+v", err)
			return nil, err
		}
	}

	// 构建响应

	response := q.helper.BuildQuestListResponse(req.PondId, questData)
	entry.Infof("BountyImpl.GetQuestList success, current:%d, completed:%d", len(response.CurrentList), len(response.CompletedList))

	return response, nil
}

// RefreshQuestList 刷新悬赏委托列表
func (q *QuestBountyImpl) RefreshQuestList(ctx context.Context, req *model.QuestListRequest, questData *model.PlayerQuestData) error {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("BountyImpl.RefreshQuestList playerId:%d, pondId:%d", req.PlayerId, req.PondId)

	// 刷新逻辑
	if err := q.helper.RefreshQuestList(ctx, req, questData); err != nil {
		return err
	}

	entry.Infof("BountyImpl.RefreshQuestList success")
	return nil
}

// ValidateAndSubmit 验证并提交悬赏委托
func (q *QuestBountyImpl) ValidateAndSubmit(ctx context.Context, req *model.QuestValidationContext, questData *model.PlayerQuestData) error {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("BountyImpl.ValidateAndSubmit playerId:%d, questId:%d", req.PlayerId, req.QuestId)
	if len(req.SelectedFishIds) == 0 {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "no fish selected")
	}
	// 验证并移除鱼护中的鱼
	if err := q.validateAndRemoveFishFromBag(ctx, req, questData); err != nil {
		return err
	}
	// 移动到完成列表
	questData.MoveToCompleted(req.QuestId, req.PondId)

	// 刷新任务
	refreshReq := req.BuildRefreshRequest()
	if err := q.RefreshQuestList(ctx, refreshReq, questData); err != nil {
		return err
	}

	entry.Infof("BountyImpl.ValidateAndSubmit success")
	return nil
}

// ProcessReward 处理悬赏委托奖励
func (q *QuestBountyImpl) ProcessReward(ctx context.Context, req *model.QuestRewardContext) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("BountyImpl.ProcessReward playerId:%d, questId:%d", req.PlayerId, req.QuestId)

	var rewardList []*commonPB.ItemBase
	for _, reward := range req.QuestReward.Rewards {
		rewardList = append(rewardList, &commonPB.ItemBase{
			ItemId:    reward.ItemId,
			ItemCount: reward.Count,
		})
	}

	// 发放奖励
	rewardInfo, err := q.helper.GrantRewards(ctx, req.PlayerId, rewardList)
	if err != nil {
		entry.Errorf("grant rewards failed: %+v", err)
		return nil, fmt.Errorf("grant rewards failed: %w", err)
	}

	entry.Infof("BountyImpl.ProcessReward success, granted %d rewards", len(rewardList))
	return rewardInfo, nil
}

// PostSubmitHook 提交后后处理
func (q *QuestBountyImpl) PostSubmitHook(ctx context.Context, req *model.QuestHookContext) error {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("BountyImpl.PostSubmitHook playerId:%d, questId:%d", req.PlayerId, req.QuestId)

	// 埋点
	safego.Go(func() {
		record.PubQuestRecord(ctx, req)
	})
	return nil
}

// validateAndRemoveFishFromBag 验证并移除鱼护中的鱼
func (q *QuestBountyImpl) validateAndRemoveFishFromBag(ctx context.Context, req *model.QuestValidationContext, questData *model.PlayerQuestData) error {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("validateAndRemoveFishFromBag req :%+v qd: %+v", req, questData)

	// 获取玩家鱼护
	fishInfo, err := rpcSpot.GetKeepnetFishInfo(ctx, req.PlayerId)
	if err != nil {
		return fmt.Errorf("get fish bag detail failed: %s", err)
	}
	if fishInfo == nil {
		return fmt.Errorf("fish info cannot be nil")
	}

	fishMap := make(map[string]*commonPB.FishDetailInfo)
	for _, fish := range fishInfo.FishInfo {
		fishMap[fish.InstanceId] = fish
	}

	// 验证鱼
	if err = q.helper.ValidateAllSelectedFish(ctx, req.QuestCond, req.SelectedFishIds, fishMap); err != nil {
		return fmt.Errorf("fish validation failed: %w", err)
	}

	// 移除鱼
	rsp, err := rpcSpot.BatchOperateFish(ctx, req.PondId, commonPB.FISH_KEEPNET_OPT_TYPE_FKOT_DEDUCT, req.SelectedFishIds)
	if err != nil {
		return fmt.Errorf("batch operate fish failed: %w", err)
	}
	if rsp == nil {
		return fmt.Errorf("batch operate fish rsp is nil")
	}
	if rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		return fmt.Errorf("batch operate fish failed: %s", rsp.Ret.Desc)
	}

	entry.Infof("validateAndRemoveFishFromBag success")
	return nil
}
