package quest_dispose

import (
	"context"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// IQuestDispose 任务处理接口
type IQuestDispose interface {
	// GetQuestList 获取任务列表
	GetQuestList(ctx context.Context, req *model.QuestListRequest) (*model.QuestListResponse, error)

	// RefreshQuestList 刷新任务列表的特殊处理
	RefreshQuestList(ctx context.Context, req *model.QuestListRequest, questData *model.PlayerQuestData) error

	// ValidateAndSubmit 验证并提交任务
	ValidateAndSubmit(ctx context.Context, req *model.QuestValidationContext, questData *model.PlayerQuestData) error

	// ProcessReward 处理任务奖励
	ProcessReward(ctx context.Context, req *model.QuestRewardContext) (*commonPB.Reward, error)

	// PostSubmitHook 提交后的后处理钩子
	PostSubmitHook(ctx context.Context, req *model.QuestHookContext) error
}
