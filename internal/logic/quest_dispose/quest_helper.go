package quest_dispose

import (
	"context"
	"fmt"
	dao_quest "tasksrv/internal/dao/quest"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

// QuestHelper 任务处理辅助工具
// 提供通用的数据访问、验证、配置管理功能
type QuestHelper struct {
	questMode commonPB.QuestMode
}

// NewQuestHelper 创建任务辅助工具
func NewQuestHelper(questMode commonPB.QuestMode) *QuestHelper {
	return &QuestHelper{
		questMode: questMode,
	}
}

// ===== 数据访问方法 =====

// GetPlayerQuestData 获取玩家任务数据
func (h *QuestHelper) GetPlayerQuestData(ctx context.Context, req *model.QuestListRequest, date string) (*model.PlayerQuestData, error) {
	return dao_quest.GetPlayerQuestData(ctx, req.QuestMode, req.PlayerId, date)
}

// SavePlayerQuestData 保存玩家任务数据
func (h *QuestHelper) SavePlayerQuestData(ctx context.Context, req *model.QuestListRequest, date string, data *model.PlayerQuestData) error {
	return dao_quest.SavePlayerQuestData(ctx, req.QuestMode, req.PlayerId, date, data)
}

// ===== 验证方法 =====

// ValidateBasicRequest 验证基础请求参数
func (h *QuestHelper) ValidateBasicRequest(ctx context.Context, req *model.QuestListRequest, questModeCfg *cmodel.QuestMode) error {
	if !questModeCfg.IsOpen {
		return protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "quest mode not open")
	}

	// todo 校验玩家等级

	return nil
}

// ValidateBasicSubmission 验证基础提交参数
func (h *QuestHelper) ValidateBasicSubmission(ctx context.Context, req *model.QuestValidationContext) error {
	if len(req.SelectedFishIds) == 0 {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "no fish selected")
	}

	return nil
}

// BuildQuestListResponse 构建任务列表响应
func (h *QuestHelper) BuildQuestListResponse(pondID int64, questData *model.PlayerQuestData) *model.QuestListResponse {

	return &model.QuestListResponse{
		CurrentList:   questData.CurrentList[pondID],
		CompletedList: questData.CompletedList,
		DailyLimit:    questData.DailyLimit,
	}
}

// RefreshQuestList 通用的任务列表刷新逻辑
func (h *QuestHelper) RefreshQuestList(ctx context.Context, req *model.QuestListRequest, questData *model.PlayerQuestData) error {
	if questData == nil {
		return fmt.Errorf("questData cannot be nil")
	}

	// 获取任务模式配置
	questModeCfg := cmodel.GetQuestMode(int64(req.QuestMode), consul_config.WithGrpcCtx(ctx))
	if questModeCfg == nil {
		return fmt.Errorf("quest mode cfg cannot be nil")
	}

	// 获取任务池
	questPool := cmodel.GetAllQuestPool(consul_config.WithGrpcCtx(ctx))
	if questPool == nil {
		return fmt.Errorf("quest pool cannot be nil")
	}

	poolMap := make(map[int64]cmodel.QuestPool)
	for _, pool := range questPool {
		if pool.PondId == req.PondId {
			poolMap[pool.Id] = *pool
		}
	}

	// 删除已完成的任务和当前任务
	for _, quest := range questData.CompletedList {
		delete(poolMap, quest.QuestId)
	}

	// 获取背包位置
	curIndexMap := make(map[int]struct{})
	for _, curQuest := range questData.CurrentList[req.PondId] {
		delete(poolMap, curQuest.QuestId)
		curIndexMap[int(curQuest.Index)] = struct{}{}
	}

	newIndexMap := make(map[int]struct{})
	for i := 1; i <= int(questModeCfg.DisplayCount); i++ {
		if _, ok := curIndexMap[i]; !ok {
			newIndexMap[i] = struct{}{}
		}
	}

	for newPoolId := range newIndexMap {
		for _, pool := range poolMap {
			delete(poolMap, pool.Id)
			newQuest := model.NewQuestInfo(pool.Id, req.PondId, int32(newPoolId))
			questData.AddCurrentQuest(newQuest)
			// 记录最近刷新的任务
			questData.LastRefreshQuest = newQuest
			break
		}
	}
	return nil
}

// GrantRewards 发放奖励
func (h *QuestHelper) GrantRewards(ctx context.Context, playerId uint64, rewards []*commonPB.ItemBase) (*commonPB.Reward, error) {
	rewardInfo, err := item_kit.SendReward(ctx, playerId, rewards, commonPB.ITEM_SOURCE_TYPE_IST_TASK_REWARD, false)
	if err != nil {
		return nil, fmt.Errorf("grant rewards failed: %w", err)
	}

	logrus.Infof("grant rewards success, playerId:%d, rewards count:%d", playerId, len(rewards))
	return rewardInfo, nil
}

// getFishValue 根据target获取鱼的对应属性值
func (h *QuestHelper) getFishValue(target int32, fishDetail *commonPB.FishDetailInfo) (int64, error) {
	if target == 0 {
		return 0, nil
	}

	if fishDetail.FishInfo == nil {
		return 0, fmt.Errorf("fish info is nil")
	}

	switch commonPB.QuestCondType(target) {
	case commonPB.QuestCondType_QCT_FISH_WEIGHT: // 鱼重
		val := fishDetail.FishInfo.Weight
		return int64(val), nil
	case commonPB.QuestCondType_QCT_FISH_SPECIAL: // 鱼品种
		val := fishDetail.FishInfo.Special
		return val, nil

	default:
		logrus.Warnf("fish value not exist target:%d fishDetail:%+v", target, fishDetail)
		return 0, fmt.Errorf("unknown target type: %d", target)
	}
}

// ValidateAllSelectedFish 验证所有选中的鱼是否满足任务条件
func (h *QuestHelper) ValidateAllSelectedFish(ctx context.Context, condition *cmodel.QuestCond, selectedFishIds []string, fishMap map[string]*commonPB.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)

	// 基础参数验证
	if err := h.validateFishParams(selectedFishIds, condition, fishMap); err != nil {
		entry.Warnf("fish params validation failed: %v", err)
		return err
	}

	entry.Infof("validating %d selected fish against quest condition %d", len(selectedFishIds), condition.Id)

	// 验证数量条件
	if err := h.validateFishCountConditions(condition.Details, int64(len(selectedFishIds))); err != nil {
		return err
	}

	// 验证每条鱼的其他条件
	if err := h.validateFishDetailConditions(ctx, condition.Details, selectedFishIds, fishMap); err != nil {
		return err
	}

	entry.Infof("all %d selected fish passed validation for quest condition %d", len(selectedFishIds), condition.Id)
	return nil
}

// validateFishParams 验证基础参数
func (h *QuestHelper) validateFishParams(selectedFishIds []string, condition *cmodel.QuestCond, fishMap map[string]*commonPB.FishDetailInfo) error {
	if len(selectedFishIds) == 0 {
		return fmt.Errorf("no fish selected")
	}
	if condition == nil {
		return fmt.Errorf("quest condition cannot be nil")
	}
	if len(fishMap) == 0 {
		return fmt.Errorf("fish map is empty")
	}
	return nil
}

// validateFishCountConditions 验证鱼数量条件
func (h *QuestHelper) validateFishCountConditions(conditions []cmodel.QuestCondDetails, fishCount int64) error {
	for _, cond := range conditions {
		if cond.Target == int32(commonPB.QuestCondType_QCT_FISH_NUM) {
			if !operate.CheckVal(commonPB.VAL_OPERATE(cond.Operate), fishCount, cond.Value) {
				return fmt.Errorf("fish count condition not satisfied: actual=%d, operate=%d, expected=%d", fishCount, cond.Operate, cond.Value)
			}
		}
	}
	return nil
}

// validateFishDetailConditions 验证每条鱼的详细条件
func (h *QuestHelper) validateFishDetailConditions(ctx context.Context, conditions []cmodel.QuestCondDetails, selectedFishIds []string, fishMap map[string]*commonPB.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)

	nonCountConditions := h.filterNonCountConditions(conditions)
	if len(nonCountConditions) == 0 {
		return nil
	}

	entry.Infof("validating %d non-count conditions for each fish", len(nonCountConditions))
	for i, fishId := range selectedFishIds {
		fishDetail, ok := fishMap[fishId]
		if !ok {
			return fmt.Errorf("fish %s not found", fishId)
		}

		if err := h.validateSingleFishConditions(ctx, nonCountConditions, fishDetail); err != nil {
			return fmt.Errorf("fish %d (id=%s) validation failed: %w", i+1, fishId, err)
		}
	}
	return nil
}

// filterNonCountConditions 过滤掉数量条件，返回其他条件
func (h *QuestHelper) filterNonCountConditions(conditions []cmodel.QuestCondDetails) []cmodel.QuestCondDetails {
	var nonCountConditions []cmodel.QuestCondDetails
	for _, cond := range conditions {
		if cond.Target != int32(commonPB.QuestCondType_QCT_FISH_NUM) {
			nonCountConditions = append(nonCountConditions, cond)
		}
	}
	return nonCountConditions
}

// validateSingleFishConditions 验证单条鱼的条件
func (h *QuestHelper) validateSingleFishConditions(ctx context.Context, conditions []cmodel.QuestCondDetails, fishDetail *commonPB.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)

	for i, cond := range conditions {
		val, err := h.getFishValue(cond.Target, fishDetail)
		if err != nil {
			entry.Warnf("get fish value failed for target %d: %v", cond.Target, err)
			return fmt.Errorf("condition %d failed: %w", i+1, err)
		}

		if !operate.CheckVal(commonPB.VAL_OPERATE(cond.Operate), val, cond.Value) {
			return fmt.Errorf("target=%d, actual=%d, operate=%d, expected=%d", cond.Target, val, cond.Operate, cond.Value)
		}
	}
	return nil
}
