package quest_dispose

import (
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// QuestDisposeFactory 任务处理器工厂
type QuestDisposeFactory struct {
	disposers map[commonPB.QuestMode]IQuestDispose
	mu        sync.RWMutex
}

var (
	factory     *QuestDisposeFactory
	factoryOnce sync.Once
)

// GetFactory 获取工厂单例
func GetFactory() *QuestDisposeFactory {
	factoryOnce.Do(func() {
		factory = &QuestDisposeFactory{
			disposers: make(map[commonPB.QuestMode]IQuestDispose),
		}
		factory.registerDefaultDisposers()
	})
	return factory
}

// registerDefaultDisposers 注册默认的任务处理器
func (f *QuestDisposeFactory) registerDefaultDisposers() {
	f.Register(commonPB.QuestMode_QR_BOUNTY, NewQuestBountyImpl())
}

// Register 注册任务处理器
func (f *QuestDisposeFactory) Register(questMode commonPB.QuestMode, disposer IQuestDispose) error {

	if disposer == nil {
		return fmt.Errorf("disposer cannot be nil")
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	f.disposers[questMode] = disposer
	return nil
}

// GetQuestDispose 获取任务处理器
func (f *QuestDisposeFactory) GetQuestDispose(questMode commonPB.QuestMode) (IQuestDispose, error) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	disposer, exists := f.disposers[questMode]
	if !exists {
		return nil, fmt.Errorf("no disposer registered for quest mode: %v", questMode)
	}

	return disposer, nil
}
