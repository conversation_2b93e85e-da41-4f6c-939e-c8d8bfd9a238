package logic_task_progress

import (
	"context"

	dao_task_progress "tasksrv/internal/dao/task_progress"
	"tasksrv/internal/logic/dispose"
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/ldy105cn/xorm"
)

// GetTaskProgress 获取任务奖励进度
func GetTaskProgress(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*commonPB.TaskProgress, error) {
	// FlushScore(ctx, playerId)
	list, err := dao_task_progress.GetAll(ctx, playerId, category)
	if err != nil {
		return nil, err
	}
	rtnList := make([]*commonPB.TaskProgress, 0)
	for _, info := range list {
		rtnList = append(rtnList, info.ToProto())
	}
	return rtnList, nil
}

func RewardProgress(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY, subId int64, rewardId int64) (*model.TaskRewardProgressVo, error) {
	entry := logx.NewLogEntry(ctx)
	info, err := dao_task_progress.GetOne(ctx, playerId, category, subId)
	if err != nil {
		entry.Errorf("get task progress error: %v", err)
		return nil, err
	}
	// 检查已领奖标记
	for _, id := range info.Rewarded {
		if id == rewardId {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "reward already received")
		}
	}

	d := dispose.GetDispose(int32(category))

	rewardList, err := d.ProgressReward(ctx, playerId, info, rewardId)
	if err != nil {
		return nil, err
	}

	// 填充领奖标记
	info.Rewarded = append(info.Rewarded, rewardId)

	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		// 数据库获取失败
		entry.Warnf("session get error:%v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get session")
	}

	rewardBack, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		err := dao_task_progress.ModifyAuto(ctx, session, playerId, info)
		if err != nil {
			return nil, err
		}

		rewardBack, err := item_kit.SendReward(ctx, playerId, rewardList, commonPB.ITEM_SOURCE_TYPE_IST_TASK_POND_PROGRESS_REWARD, false)
		if err != nil {
			return nil, err
		}
		return rewardBack, nil
	})

	if err != nil {
		return nil, err
	}
	rsp := &model.TaskRewardProgressVo{
	}
	if rewardBack != nil {
		rsp.Reward =  rewardBack.(*commonPB.Reward)
	}
	rsp.TTaskProgress = info

	return rsp, nil
}

// 获取缓存进度
// func GetTaskProgress(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) (*model.TTaskProgress, error) {
// 	// TODO: 优化该sql
// 	pondList, err := dao_task_progress.GetOne(ctx, playerId, category, subId)
// 	if err != nil {
// 		return nil, err
// 	}

// 	for _, data := range pondList {
// 		if data.PondId == pondId {
// 			return data, nil
// 		}
// 	}
// 	for _, cfg := range cmodel.GetAllTaskPond(consul_config.WithGrpcCtx(ctx)) {
// 		if cfg.PondId == pondId {
// 			data := &model.TTaskPondReward{
// 				PlayerId: playerId,
// 				PondId:   pondId,
// 				Rewarded: make([]int32, 0),
// 				Score:    0,
// 			}
// 			return data, nil
// 		}
// 	}

// 	return nil, fmt.Errorf("cfg task Pond %d not found", pondId)
// }
