package utility

import (
	"context"
	dao_task "tasksrv/internal/dao/task"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// CheckCanReward 检查是否可以发放奖励
func CheckCanReward(ctx context.Context, task *model.TTask) bool {
	entry := logx.NewLogEntry(ctx)
	if task.Status != int32(commonPB.TASK_STATUS_TS_DURING) {
		return false
	}
	taskCfg := cmodel.GetTask(task.TaskId, consul_config.WithGrpcCtx(ctx))
	if taskCfg == nil {
		entry.Warnf("task cfg id[%d] not found", task.TaskId)
		return false
	}
	condGroupCfg := cmodel.GetTaskCondGroup(taskCfg.CondGroup, consul_config.WithGrpcCtx(ctx))
	if condGroupCfg == nil {
		entry.Warnf("task cfg id[%d] not found", taskCfg.CondGroup)
		return false
	}

	condCfgMap := cmodel.GetAllTaskCond(consul_config.WithGrpcCtx(ctx))
	for _, condId := range condGroupCfg.Conds {
		cond := task.GetCond(condId)
		if cond == nil {
			return false
		}
		condCfg := condCfgMap[condId]
		if condCfg.Target > cond.Progress {
			return false
		}
	}
	return true
}

// 基于任务类型获取任务
func GetTaskByCategory(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*model.TTask, error) {
	dataList, err := dao_task.GetAll(ctx, playerId)
	if err != nil {
		return nil, err
	}

	rtn := make([]*model.TTask, 0)
	for _, q := range dataList {
		if q.Category == category {
			rtn = append(rtn, q)
		}
	}
	return rtn, nil
}
