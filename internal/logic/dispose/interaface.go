package dispose

import (
	"context"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

type IDisposeTask interface {
	// 领取的特殊处理
	Reward(ctx context.Context, playerId uint64, task *model.TTask) (*commonPB.Reward, error)
	// 积分增加
	AddScore(ctx context.Context, playerId uint64, task *model.TTask) (*model.TTaskProgress, error)

	// 进度奖励
	ProgressReward(ctx context.Context, playerId uint64, progress *model.TTaskProgress, rewardId int64) ([]*commonPB.ItemBase, error)

	// 前提检测
	CondPremise(ctx context.Context, taskCfg *cmodel.Task, event *commonPB.EventCommon) bool

	// 设置红点
	SetRedPoint(ctx context.Context, playerId uint64, progress *model.TTaskProgress)

	//  后续走统一逻辑
	// OpenCheck(ctx context.Context, playerId uint64, accepted []*model.TTask, taskCfg *cmodel.Task) (context.Context, bool)

	// 完成后的特殊处理
}
