package dispose

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// GetDispose 获取处理模型
func GetDispose(category int32) IDisposeTask {
	var dispose IDisposeTask
	switch category {
	case int32(commonPB.TASK_CATEGORY_TC_POND):
		dispose = &DisposeExploreImpl{}
		return dispose
	case int32(commonPB.TASK_CATEGORY_TC_ACHIEVE):
		dispose = &DisposeAchieveImpl{}
		return dispose
	default:
		dispose = &DisposeDefaultImpl{}
		// logrus.Errorf("dispose type error:%d", category)
		return dispose
	}
}
