package dispose

import (
	"context"
	"fmt"
	"tasksrv/config"
	"tasksrv/internal/dao/task_progress"
	"tasksrv/internal/model"

	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

type DisposeAchieveImpl struct {
	DisposeDefaultImpl
}

// AddScore 增加成就积分
func (d *DisposeAchieveImpl) AddScore(ctx context.Context, playerId uint64, task *model.TTask) (*model.TTaskProgress, error) {
	entry := logx.NewLogEntry(ctx)
	taskCfg := cmodel.GetTask(task.TaskId, consul_config.WithGrpcCtx(ctx))
	if taskCfg == nil {
		entry.Errorf("task config not found:%+v", task.TaskId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("task config not found:%+v", task.TaskId))
	}
	progressList, err := task_progress.GetAll(ctx, playerId, task.Category)
	if err != nil {
		return nil, err
	}

	// 成就任务只有一个积分进度
	var subId int64 = 0
	// 获取指定progress
	newProgress := model.NewProgress(playerId, task.Category, subId)
	for _, progress := range progressList {
		if progress.SubId == subId {
			newProgress = progress
		}
	}

	newProgress.Score += taskCfg.Score
	return newProgress, nil
}

// SetRedPoint 设置红点
func (d *DisposeAchieveImpl) SetRedPoint(ctx context.Context, playerId uint64, progress *model.TTaskProgress) {
	entry := logx.NewLogEntry(ctx)

	// 获取配置开关
	switchCfg := cmodel.GetFeatureHide(config.AchieveRedDotID, consul_config.WithGrpcCtx(ctx))
	if switchCfg == nil {
		entry.Errorf("achieve red_dot switch config not found:%+v", config.AchieveRedDotID)
		return
	}
	if commonPB.FEATURE_HIDE_TYPE(switchCfg.HideType) != commonPB.FEATURE_HIDE_TYPE_FHT_NONE {
		entry.Debugf("achieve red_dot switch is off:%+v", config.AchieveRedDotID)
		return
	}

	acType := int32(commonPB.TASK_CATEGORY_TC_ACHIEVE)
	// 已领取奖励
	rewarded := make(map[int64]bool)
	for _, v := range progress.Rewarded {
		rewarded[v] = true
	}

	needRedPoint := false
	taskProgressCfg := cmodel.GetAllTaskProgress(consul_config.WithGrpcCtx(ctx))
	for _, cfg := range taskProgressCfg {
		// 成就任务 并且 当前进度 满足配置 并且未在已奖励
		if cfg.Type == acType && cfg.Score <= progress.Score && !rewarded[cfg.Score] {
			needRedPoint = true
			break
		}
	}

	// 需要设置红点
	if needRedPoint {
		hallRpcCli := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
		_, err := hallRpcCli.SetPlayerRedDot(ctx, &hallRpc.SetPlayerRedDotReq{
			PlayerId:   playerId,
			ModuleType: commonPB.USER_MODULE_TYPE_UMT_ACHIEVEMENT,
			SubModuleType: []int32{
				int32(commonPB.RED_DOT_ACHIEVEMENT_SUB_TYPE_RDAST_DEFAULT),
			},
			HasRedDot: true,
		})
		if err != nil {
			entry.Errorf("set mail red dot failed, playerId:%d, err:%v", playerId, err)
		}
	}
	return
}
