package dispose

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 探索任务
type DisposeExploreImpl struct {
	DisposeDefaultImpl
}

// CondPremise 特定大前提
func (d *DisposeExploreImpl) CondPremise(ctx context.Context, cfg *cmodel.Task, event *commonPB.EventCommon) bool {

	// 探索任务 sub = 钓场id
	return cfg.SubId == event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND)] || cfg.SubId == 0
}
