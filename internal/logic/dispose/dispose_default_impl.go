package dispose

import (
	"context"
	"fmt"
	"tasksrv/internal/dao/task_progress"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/errorx"
)

var _ IDisposeTask = &DisposeDefaultImpl{}

type DisposeDefaultImpl struct {
}

// 发放任务奖励
func (d *DisposeDefaultImpl) Reward(ctx context.Context, playerId uint64, task *model.TTask) (*commonPB.Reward, error) {
	entry := logx.NewLogEntry(ctx)
	_ = entry
	taskId := task.TaskId
	cfg := cmodel.GetTask(taskId, consul_config.WithGrpcCtx(ctx))
	if cfg == nil {
		return nil, fmt.Errorf("cfg task[%d] not found", taskId)
	}
	// 不发放奖励
	if cfg.Reward == 0 {
		rewardBack := &commonPB.Reward{}
		return rewardBack, nil
	}
	rewardCfg := cmodel.GetTaskReward(cfg.Reward, consul_config.WithGrpcCtx(ctx))
	if rewardCfg == nil {
		return nil, fmt.Errorf("task reward cfg[%d] not found", cfg.Reward)
	}

	rewardList := make([]*commonPB.ItemBase, len(rewardCfg.Rewards))

	for i, reward := range rewardCfg.Rewards {
		rewardList[i] = &commonPB.ItemBase{
			ItemId:    reward.ItemId,
			ItemCount: reward.Count,
		}
	}

	rewardBack, err := item_kit.SendReward(ctx, playerId, rewardList, commonPB.ITEM_SOURCE_TYPE_IST_TASK_POND_PROGRESS_REWARD, false)

	if err != nil {
		// 奖励失败
		ex := errorx.NewErrorCode(int(commonPB.ErrCode_ERR_HALL_ADD_REWARD_ERR), err.Error())
		return nil, ex
	}

	return rewardBack, nil
}

// 增加成就积分
func (d *DisposeDefaultImpl) AddScore(ctx context.Context, playerId uint64, task *model.TTask) (*model.TTaskProgress, error) {
	taskCfg := cmodel.GetTask(task.TaskId, consul_config.WithGrpcCtx(ctx))
	entry := logx.NewLogEntry(ctx)
	if taskCfg == nil {
		entry.Errorf("task config not found:%+v", task.TaskId)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("task config not found:%+v", task.TaskId))
	}
	progressList, err := task_progress.GetAll(ctx, playerId, task.Category)
	if err != nil {
		return nil, err
	}
	// 获取指定progress
	newProgress := model.NewProgress(playerId, task.Category, taskCfg.SubId)
	for _, progress := range progressList {
		if progress.SubId == taskCfg.SubId {
			newProgress = progress
		}
	}

	newProgress.Score += taskCfg.Score
	return newProgress, nil
}

func (d *DisposeDefaultImpl) ProgressReward(ctx context.Context, playerId uint64, progress *model.TTaskProgress, rewardId int64) ([]*commonPB.ItemBase, error) {
	progressCfg := cmodel.GetTaskProgress(rewardId, consul_config.WithGrpcCtx(ctx))
	if progressCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("progress reward[%d] not found", rewardId))
	}
	// 检查已领奖标记

	// 类型不匹配
	if progressCfg.Type != int32(progress.Category) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "category")
	}

	// 子类型不匹配
	if progressCfg.SubId != progress.SubId {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "subId")
	}

	if progressCfg.Score > progress.Score {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "score")
	}
	if progressCfg.Reward == 0 {
		return []*commonPB.ItemBase{}, nil
	}

	rewardCfg := cmodel.GetTaskReward(progressCfg.Reward, consul_config.WithGrpcCtx(ctx))
	if rewardCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("rewardCfg:%d", progressCfg.Reward))
	}
	rewardList := make([]*commonPB.ItemBase, 0)

	for _, item := range rewardCfg.Rewards {
		rewardList = append(rewardList, &commonPB.ItemBase{
			ItemId:    item.ItemId,
			ItemCount: item.Count,
		})
	}

	return rewardList, nil
}

// CondPremise 特定大前提
func (d *DisposeDefaultImpl) CondPremise(ctx context.Context, task *cmodel.Task, event *commonPB.EventCommon) bool {
	return true
}

// SetRedPoint 设置红点
func (d *DisposeDefaultImpl) SetRedPoint(ctx context.Context, playerId uint64, progress *model.TTaskProgress) {
	return
}
