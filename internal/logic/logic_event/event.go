package logic_event

import (
	"context"
	"fmt"
	"tasksrv/internal/logic/dispose"
	"tasksrv/internal/model"

	dao_task "tasksrv/internal/dao/task"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/statsx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

// GetEventVal 查询事件值
func GetEventVal(event *commonPB.EventCommon, key int32) int64 {
	if key == 0 {
		return 1
	}
	val, ok := event.IntData[key]
	if !ok {
		logrus.Warnf("event val not exist key:%d event:%+v", key, event)
		return 0
	}
	return val
}

// 检查任务前置条件
func CheckCondPremise(ctx context.Context, cfg *cmodel.TaskCond, event *commonPB.EventCommon) bool {
	switch cfg.Control {
	case int32(commonPB.AOI_AOI_AND):
		// 全部满足才满足
		for _, detail := range cfg.Details {
			val := GetEventVal(event, detail.Label)
			if bl := operate.CheckVal(commonPB.VAL_OPERATE(detail.Operate), val, detail.Value); !bl {
				return false
			}
		}
		return true
	case int32(commonPB.AOI_AOI_OR):
		// 一个满足则满足
		for _, detail := range cfg.Details {
			val := GetEventVal(event, detail.Label)
			if bl := operate.CheckVal(commonPB.VAL_OPERATE(detail.Operate), val, detail.Value); bl {
				return true
			}
		}
		return false
	default:
		// 未知条件
		return false
	}
}

// 检查任务变化
func EventChangeCheck(ctx context.Context, playerId uint64, event *commonPB.EventCommon) (changeList []*model.TTask, err error) {
	entry := logx.NewLogEntry(ctx)
	changeList = make([]*model.TTask, 0)

	taskCfgMap := cmodel.GetAllTask(consul_config.WithGrpcCtx(ctx))
	if len(taskCfgMap) == 0 {
		entry.Warnf("taskCfg is null")
		return nil, fmt.Errorf("task map cfg is null")
	}
	condCfgMap := cmodel.GetAllTaskCond(consul_config.WithGrpcCtx(ctx))
	if len(condCfgMap) == 0 {
		entry.Warnf("taskCondCfg is null")
		return nil, fmt.Errorf("task cond map cfg is null")
	}
	condGroupCfgMap := cmodel.GetAllTaskCondGroup(consul_config.WithGrpcCtx(ctx))
	if len(condGroupCfgMap) == 0 {
		entry.Warnf("taskCondGroupCfg is null")
		return nil, fmt.Errorf("task cond group cfg is null")
	}

	taskList, err := dao_task.GetAll(ctx, playerId)
	if err != nil {
		return nil, err
	}

	// 遍历所有任务检查
	for _, task := range taskList {
		// XXX: 考虑实时过滤掉无效条件, 性能消耗

		// 节省调用栈 不重复请求cmodel
		// 只有正在执行中才处理进度
		if task.Status != int32(commonPB.TASK_STATUS_TS_DURING) {
			continue
		}
		taskCfg, ok := taskCfgMap[task.TaskId]
		if !ok {
			entry.Warnf("task config is null, taskId:%d", task.TaskId)
			// 任务状态修改为删除
			task.Status = int32(commonPB.TASK_STATUS_TS_DELETE)
			changeList = append(changeList, task)
			continue
		}

		// 检查大前提
		dispose := dispose.GetDispose(taskCfg.Type)
		if !dispose.CondPremise(ctx, taskCfg, event) {
			continue
		}

		taskCondGroupCfg, ok := condGroupCfgMap[taskCfg.CondGroup]
		if !ok {
			if taskCfg.CondGroup != 0 {
				entry.Warnf("taskCondGroupCfg[%d] not found", taskCfg.CondGroup)
			}
			continue
		}

		// 当前任务有没有被更新
		flag := false
		// 检查配置条件
		for _, condId := range taskCondGroupCfg.Conds {
			condCfg, ok := condCfgMap[condId]
			if !ok {
				// 任务条件配置不存在
				entry.Warnf("task cond config is null, condId:%d", condId)
				continue
			}

			// 检查事件类型（大前提）
			if condCfg.Event != int64(event.EventType) {
				continue
			}
			// 检查任务前置条件
			if check := CheckCondPremise(ctx, condCfg, event); !check {
				continue
			}

			cond := task.GetCond(condId)
			if cond == nil {
				cond = statsx.NewStats(condId)
				task.CondList = append(task.CondList, cond)
				flag = true
			}

			// 只要有一个变化则更新
			isChange := cond.SumStats(commonPB.SUM_ADD(condCfg.AddType), GetEventVal(event, condCfg.Field))
			flag = flag || isChange
		}
		// 检查不通过，剔除
		if flag {
			changeList = append(changeList, task)
			continue
		}
	}

	return changeList, nil
}
