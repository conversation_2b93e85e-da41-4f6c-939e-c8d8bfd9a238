package open_check

import (
	"context"
	"tasksrv/config"
	dao_task "tasksrv/internal/dao/task"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
)

func checkRoleLev(ctx context.Context, op int32, target int64) (context.Context, bool) {
	userInfoI := ctx.Value(config.KEY_CTX_USER_INFO)
	userInfo, ok := userInfoI.(*commonPB.RichUserInfo)

	// TODO 抽象接口
	if !ok {
		// 查询用户信息
		opt := interceptor.GetRPCOptions(ctx)
		userInfoRsp, err := crpc_user.RpcGetPlayerInfo(ctx, opt.ProductId, opt.PlayerId)
		// 查询用户信息失败
		if err != nil {
			logrus.Warnf("RpcGetPlayerInfo fail: %v productId:%d playerId:%d", err, opt.ProductId, opt.PlayerId)
			return ctx, false
		}
		userInfo = userInfoRsp.GetRichUserInfo()
		if userInfo == nil {
			// 查询用户信息失败
			logrus.Warnf("RpcGetPlayerInfo fail: %v productId:%d playerId:%d", err, opt.ProductId, opt.PlayerId)
			return ctx, false
		}
		ctx = context.WithValue(ctx, config.KEY_CTX_USER_INFO, userInfo)
	}

	role_lev := userInfo.GetBriefUserInfo().GetLev()

	return ctx, operate.CheckVal(commonPB.VAL_OPERATE(op), int64(role_lev), target)
}

func checkTaskCompete(ctx context.Context, op int32, target int64) (context.Context, bool) {
	// 提取已
	taskAcceptI := ctx.Value(config.KEY_CTX_TASK_ACCEPT)
	TaskAccept, ok := taskAcceptI.([]*model.TTask)
	if !ok {
		opt := interceptor.GetRPCOptions(ctx)
		accepted, err := dao_task.GetAll(ctx, opt.PlayerId)
		if err != nil {
			return nil, false
		}
		ctx = context.WithValue(ctx, config.KEY_CTX_TASK_ACCEPT, accepted)
		TaskAccept = accepted
	}

	for _, task := range TaskAccept {
		if task.TaskId == target {
			if task.Status == int32(commonPB.TASK_STATUS_TS_COMPLETE) {
				return ctx, true
			}
		}
	}

	return ctx, false
}
