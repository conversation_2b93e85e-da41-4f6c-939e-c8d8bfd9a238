package open_check

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/sirupsen/logrus"
)

func CheckOpenList(inCtx context.Context, playerId uint64, taskCfg *cmodel.Task) bool {
	var flag bool
	ctx := inCtx

	condGroup := cmodel.GetTaskCondGroup(taskCfg.CondGroup, consul_config.WithGrpcCtx(inCtx))
	openControl := condGroup.OpenControl
	openCfgMap := cmodel.GetAllTaskOpenRule(consul_config.WithGrpcCtx(inCtx))

	switch commonPB.AOI(openControl) {
	case (commonPB.AOI_AOI_AND):
		for _, check := range condGroup.OpenConds {
			if check.OpenCond == 0 {
				continue
			}
			ruleCfg := openCfgMap[check.OpenCond]
			if ruleCfg == nil {
				logrus.Errorf("Open Rule %v not Found", check.OpenCond)
				return false
			}

			ctx, flag = Check(ctx, playerId, ruleCfg.Label, ruleCfg.Operate, check.OpenVal)
			if !flag {
				return false
			}
		}
		return true
	case (commonPB.AOI_AOI_OR):
		for _, check := range condGroup.OpenConds {
			if check.OpenCond == 0 {
				continue
			}
			ruleCfg := openCfgMap[check.OpenCond]
			if ruleCfg == nil {
				logrus.Errorf("Open Rule %v not Found", check.OpenCond)
				return false
			}

			ctx, flag = Check(ctx, playerId, ruleCfg.Label, ruleCfg.Operate, check.OpenVal)
			if !flag {
				return true
			}
		}
		return false
	default:
		return false
	}
	// return false
}

func Check(ctx context.Context, playerId uint64, label int32, operate int32, check int64) (context.Context, bool) {
	entry := logx.NewLogEntry(ctx)
	switch commonPB.TASK_OPEN(label) {
	case commonPB.TASK_OPEN_TO_ROLE_LEV_GT:
		return checkRoleLev(ctx, operate, check)
	case commonPB.TASK_OPEN_TO_TASK_COMPLETE:
		return checkTaskCompete(ctx, operate, check)
	default:
		entry.Errorf("openType[%d] no support", label)
		return ctx, false
	}
}
