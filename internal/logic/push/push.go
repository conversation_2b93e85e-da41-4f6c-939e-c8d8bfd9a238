package push

import (
	"context"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/sender"
)

// TaskUpdateNTF 任务状态广播
func TaskUpdateNTF(ctx context.Context, playerId uint64, changeList ...*model.TTask) {
	if len(changeList) == 0 {
		return
	}
	list := make([]*commonPB.TaskInfo, len(changeList))
	for i, one := range changeList {
		list[i] = one.ToProto()
	}
	_ = sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_TASK_UPDATE_NTF, &taskPB.UpdateTaskNTF{
		Ret:  protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS),
		Info: list,
	})
}

// 任务进度模式广播
func TaskProgressNTF(ctx context.Context, playerId uint64, changeList ...*model.TTaskProgress) {
	if len(changeList) == 0 {
		return
	}

	mp := make(map[commonPB.TASK_CATEGORY][]*commonPB.TaskProgress)
	for _, one := range changeList {
		if mp[one.Category] == nil {
			mp[one.Category] = make([]*commonPB.TaskProgress, 0)
		}
		mp[one.Category] = append(mp[one.Category], one.ToProto())
	}

	// 根据类型分类
	for category, list := range mp {
		_ = sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_TASK_PROGRESS_NTF, &taskPB.TaskProgressNTF{
			Category: category,
			List:     list,
		})

	}

}
