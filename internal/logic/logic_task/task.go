package logic_task

import (
	"context"
	"fmt"
	"tasksrv/config"
	dao_task "tasksrv/internal/dao/task"
	dao_task_progress "tasksrv/internal/dao/task_progress"
	"tasksrv/internal/logic/dispose"
	"tasksrv/internal/logic/open_check"
	"tasksrv/internal/logic/push"
	"tasksrv/internal/logic/utility"
	"tasksrv/internal/model"
	"tasksrv/internal/pubsub/publish"
	"tasksrv/internal/repo/mysql_mgr"
	"tasksrv/internal/repo/record"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
)

// 分布式锁操作锁d
func lockKey(playerId uint64) string {
	return config.DLMTaskLockKey(playerId)
}

// TaskAccept 领取任务
func TaskAccept(inCtx context.Context, playerId uint64) (acceptTask []*model.TTask, err error) {
	ctx := inCtx
	accepted, err := dao_task.GetAll(ctx, playerId)
	if err != nil {
		return nil, err
	}
	ctx = context.WithValue(ctx, config.KEY_CTX_TASK_ACCEPT, accepted)
	// 检查领取条件
	taskListCfg := cmodel.GetAllTask(consul_config.WithGrpcCtx(inCtx))

	newTaskList, changeList := CleanTask(taskListCfg, accepted)

	// 转换id序列
	acceptedIds := make(map[int64]*model.TTask)
	for _, q := range newTaskList {
		acceptedIds[q.TaskId] = q
	}

	for _, taskCfg := range taskListCfg {
		// 已领取任务跳过，未来可以检查任务内容变化
		// 任务刷新检查
		if acceptedIds[taskCfg.Id] != nil {
			// 任务状态过滤
			continue
		}

		var flag bool
		// dispose := dispose.GetDispose(taskCfg.Type)
		// if dispose != nil {
		// ctx, flag = dispose.OpenCheck(ctx, playerId, accepted, taskCfg)
		// if !flag {
		// 	continue
		// }
		// }

		flag = open_check.CheckOpenList(ctx, playerId, taskCfg)
		// flag = true
		if flag {
			newTask := model.NewTaskByCfg(taskCfg)
			newTask.PlayerId = playerId
			ctx, _ = FlushInitTask(ctx, newTask)

			changeList = append(changeList, newTask)
		}
	}

	// 存储
	// 初始化CondList
	if len(changeList) == 0 {
		return changeList, nil
	}

	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		return nil, err
	}
	session := engine.NewSession()
	defer session.Close()
	session.Begin()
	{
		errDao := dao_task.ModifyAuto(ctx, session, playerId, changeList...)
		if errDao != nil {
			session.Rollback()
			return nil, err
		}
	}
	session.Commit()
	safego.Go(func() {
		record.PubTaskRecord(ctx, playerId, changeList)
	})

	return changeList, nil
}

// 清理任务
func CleanTask(taskListCfg map[int64]*cmodel.Task, taskList []*model.TTask) (newList, delList []*model.TTask) {
	newList = make([]*model.TTask, 0)
	delList = make([]*model.TTask, 0)
	for _, task := range taskList {
		_, ok := taskListCfg[task.TaskId]
		if !ok {
			task.Status = int32(commonPB.TASK_STATUS_TS_DELETE)
			delList = append(delList, task)
		}
		newList = append(newList, task)
	}

	return
}

// 刷新任务初始值
func FlushInitTask(ctx context.Context, task *model.TTask) (context.Context, error) {
	return ctx, nil
}

// Reward 任务发放奖励
func Reward(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY, taskId int64) (*taskPB.RewardTaskRsp, error) {
	rsp := &taskPB.RewardTaskRsp{
		Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS),
	}
	entry := logx.NewLogEntry(ctx)
	entry.Debug("reward task start")
	// 交互锁
	// TODO 应该使用非公平锁
	unlock := dlm.LockKey(lockKey(playerId))
	defer unlock()

	entry.WithFields(logrus.Fields{
		"category": category,
		"taskId":   taskId,
	})

	entry.Debugf("reward task lock start")

	// 查询任务
	task, err := dao_task.Get(ctx, playerId, category, taskId)
	if err != nil {
		// 查询失败
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
	}

	if task.Status != int32(commonPB.TASK_STATUS_TS_DURING) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	// 检查任务条件
	ok := utility.CheckCanReward(ctx, task)
	if !ok {
		// 任务未完成
		return nil, protox.CodeError(commonPB.ErrCode_ERR_TASK_NOT_FINISH)
	}
	entry.Debugf("reward task check finish")

	dispose := dispose.GetDispose(int32(task.Category))
	if dispose == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_TASK_NOT_DISPOSE, fmt.Sprintf("error task category:%d", task.Category))
	}

	// 更新任务状态
	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		// 数据库获取失败
		entry.Warnf("session get error:%v", err)
		return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "get session")
	}
	task.Status = int32(commonPB.TASK_STATUS_TS_COMPLETE)
	newProgress, err := dispose.AddScore(ctx, playerId, task)
	if err != nil {
		return nil, err
	}

	rewardBack, err := engine.Transaction(func(session *xorm.Session) (interface{}, error) {
		err = dao_task.ModifyAuto(ctx, session, playerId, task)
		if err != nil {
			// 更新状态失败
			entry.Warnf("session update error:%v", err)
			return nil, protox.CodeError(commonPB.ErrCode_ERR_DB_OPE_ERROR, "data update")
		}

		if newProgress != nil {
			err := dao_task_progress.ModifyAuto(ctx, session, playerId, newProgress)
			if err != nil {
				return nil, err
			}
			// 红点逻辑
			dispose.SetRedPoint(ctx, playerId, newProgress)
		}

		entry.Debugf("reward task dispose reward")

		// 发放额外奖励
		rewardBack, err := dispose.Reward(ctx, playerId, task)
		if err != nil {
			return nil, err
		}

		return rewardBack, nil
	})
	if err != nil {
		return nil, err
	}

	entry.Debugf("reward task update dao")
	if rewardBack != nil {
		rsp.Reward = rewardBack.(*commonPB.Reward)
	}
	if newProgress != nil {
		push.TaskProgressNTF(ctx, playerId, newProgress)
	}

	rsp.Info = task.ToProto()

	entry.Debugf("reward task success")
	safego.Go(func() {
		record.PubTaskRecord(ctx, playerId, []*model.TTask{task})
	})
	publish.PublishTaskComplete(ctx, playerId, task)
	return rsp, nil
}
