package model

import (
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"tasksrv/config"
	"time"
)

// QuestInfo 任务信息（合并原QuestDetail，因为结构完全相同）
type QuestInfo struct {
	QuestId int64 `json:"quest_id"` // 任务唯一ID
	PondId  int64 `json:"pond_id"`  // 钓场ID
	Index   int32 `json:"index"`    // 显示位置
}

// QuestDetail 任务列表项的别名，保持向后兼容
type QuestDetail = QuestInfo

// PlayerQuestData 玩家任务数据（Redis存储）
type PlayerQuestData struct {
	CurrentList      map[int64][]*QuestInfo `json:"current_list"`   // 玩家当前任务列表
	CompletedList    []*QuestInfo           `json:"completed_list"` // 玩家已完成任务列表
	DailyLimit       int32                  `json:"daily_limit"`    // 每日完成上限
	UpdateTime       int64                  `json:"update_time"`    // 更新时间
	LastRefreshQuest *QuestInfo             `json:"-"`              // 最近刷新的任务
}

// 实现通用的JSON序列化方法
func (q *QuestInfo) FromJson(b []byte) error {
	return json.Unmarshal(b, q)
}

func (q *QuestInfo) ToJson() []byte {
	str, _ := json.Marshal(q)
	return str
}

func (p *PlayerQuestData) FromJson(b []byte) error {
	return json.Unmarshal(b, p)
}

func (p *PlayerQuestData) ToJson() []byte {
	str, _ := json.Marshal(p)
	return str
}

// ToProto 转换为Proto消息
func (q *QuestInfo) ToProto() *commonPB.QuestList {
	return &commonPB.QuestList{
		QuestId:      q.QuestId,
		PondId:       q.PondId,
		DisplayIndex: q.Index,
	}
}

// ===== 请求和响应模型 =====

// QuestListRequest 获取任务列表请求
type QuestListRequest struct {
	PlayerId  uint64             `json:"player_id"`  // 玩家ID
	PondId    int64              `json:"pond_id"`    // 钓场ID
	QuestMode commonPB.QuestMode `json:"quest_mode"` // 任务模式
}

// QuestListResponse 获取任务列表响应
type QuestListResponse struct {
	CurrentList   []*QuestInfo `json:"current_list"`   // 当前任务列表
	CompletedList []*QuestInfo `json:"completed_list"` // 已完成任务列表
	DailyLimit    int32        `json:"daily_limit"`    // 每日完成上限
}

// QuestSubmitRequest 提交任务请求
type QuestSubmitRequest struct {
	PlayerId        uint64             `json:"player_id"`         // 玩家ID
	QuestId         int64              `json:"quest_id"`          // 任务ID
	PondId          int64              `json:"pond_id"`           // 钓场ID
	QuestMode       commonPB.QuestMode `json:"quest_mode"`        // 任务模式
	SelectedFishIds []string           `json:"selected_fish_ids"` // 选中的鱼ID列表
}

// QuestSubmitResponse 提交任务响应
type QuestSubmitResponse struct {
	QuestInfo *QuestInfo           `json:"quest_info"` // 任务信息
	Rewards   []*commonPB.ItemBase `json:"rewards"`    // 获得的奖励（使用通用奖励结构）
}

// QuestValidationContext 任务验证上下文
type QuestValidationContext struct {
	PlayerId        uint64             `json:"player_id"`         // 玩家ID
	QuestId         int64              `json:"quest_id"`          // 任务ID
	PondId          int64              `json:"pond_id"`           // 钓场ID
	QuestMode       commonPB.QuestMode `json:"quest_mode"`        // 任务模式
	SelectedFishIds []string           `json:"selected_fish_ids"` // 选中的鱼ID列表
	QuestCond       *cmodel.QuestCond  `json:"quest_cond"`
	QuestPool       *cmodel.QuestPool  `json:"quest_pool"`
}

// QuestRewardContext 任务奖励上下文
type QuestRewardContext struct {
	PlayerId    uint64              `json:"player_id"`    // 玩家ID
	QuestId     int64               `json:"quest_id"`     // 任务ID
	QuestMode   commonPB.QuestMode  `json:"quest_mode"`   // 任务模式
	QuestInfo   *QuestInfo          `json:"quest_info"`   // 任务详细信息
	QuestReward *cmodel.QuestReward `json:"quest_reward"` // 任务奖励配置
}

type QuestHookContext struct {
	PlayerId        uint64              `json:"player_id"`         // 玩家ID
	QuestId         int64               `json:"quest_id"`          // 任务ID
	SelectedFishIds []string            `json:"selected_fish_ids"` // 选中的鱼ID列表
	QuestMode       commonPB.QuestMode  `json:"quest_mode"`        // 任务模式
	QuestReward     *cmodel.QuestReward `json:"quest_reward"`      // 任务奖励配置
	QuestCond       *cmodel.QuestCond   `json:"quest_cond"`        // 任务条件配置
}

// ===== 工具函数 =====

// GetQuestRedisKey 获取玩家任务Redis Key
func GetQuestRedisKey(questMode commonPB.QuestMode, playerId uint64, date string) string {
	return fmt.Sprintf(config.RedisKeyCacheQuest, int32(questMode), playerId, date)
}

// GetCurrentDate 获取当前日期
func GetCurrentDate() string {
	return timex.Now().Format("20060102")
}

// ===== 构建方法 =====

// NewQuestInfo 创建新的任务信息
func NewQuestInfo(questId, pondId int64, index int32) *QuestInfo {
	return &QuestInfo{
		QuestId: questId,
		PondId:  pondId,
		Index:   index,
	}
}

// NewQuestInfoFromMode 根据模式创建任务信息（兼容旧接口）
func NewQuestInfoFromMode(questId, pondId int64, questMode commonPB.QuestMode) *QuestInfo {
	return &QuestInfo{
		QuestId: questId,
		PondId:  pondId,
	}
}

// NewPlayerQuestData 创建新的玩家任务数据
func NewPlayerQuestData() *PlayerQuestData {
	return &PlayerQuestData{
		CurrentList:   make(map[int64][]*QuestInfo),
		CompletedList: make([]*QuestInfo, 0),
		DailyLimit:    0,
		UpdateTime:    time.Now().Unix(),
	}
}

// NewQuestListRequest 创建任务列表请求
func NewQuestListRequest(playerId uint64, req *taskPB.GetQuestListReq) *QuestListRequest {
	return &QuestListRequest{
		PlayerId:  playerId,
		PondId:    req.PondId,
		QuestMode: commonPB.QuestMode(req.QuestMode),
	}
}

// NewQuestListResponse 创建任务列表响应
func NewQuestListResponse() *QuestListResponse {
	return &QuestListResponse{
		CurrentList:   make([]*QuestInfo, 0),
		CompletedList: make([]*QuestInfo, 0),
	}
}

// NewQuestValidationContext 创建任务验证上下文
func NewQuestValidationContext(playerId uint64, req *taskPB.SubmitQuestReq, questCond *cmodel.QuestCond) *QuestValidationContext {
	return &QuestValidationContext{
		PlayerId:        playerId,
		QuestId:         req.QuestId,
		PondId:          req.PondId,
		QuestMode:       req.QuestMode,
		SelectedFishIds: req.SelectedFishIds,
		QuestCond:       questCond,
	}
}

// NewQuestRewardContext 创建任务奖励上下文
func NewQuestRewardContext(playerId uint64, questId int64, questMode commonPB.QuestMode, questReward *cmodel.QuestReward) *QuestRewardContext {
	return &QuestRewardContext{
		PlayerId:    playerId,
		QuestId:     questId,
		QuestMode:   questMode,
		QuestReward: questReward,
	}
}

// NewQuestHookContext 创建任务后处理钩子上下文
func NewQuestHookContext(playerId uint64, req *taskPB.SubmitQuestReq, questCond *cmodel.QuestCond, questReward *cmodel.QuestReward) *QuestHookContext {
	return &QuestHookContext{
		PlayerId:        playerId,
		QuestId:         req.QuestId,
		QuestMode:       req.QuestMode,
		SelectedFishIds: req.SelectedFishIds,
		QuestCond:       questCond,
		QuestReward:     questReward,
	}
}

// ===== Proto转换方法 =====

// FromProto 从 commonPB.QuestList 转换为 QuestInfo
func (q *QuestInfo) FromProto(pb *commonPB.QuestList) {
	if pb != nil {
		q.QuestId = pb.QuestId
		q.PondId = pb.PondId
		q.Index = pb.DisplayIndex
	}
}

// ===== 业务方法 =====

// Clone 深拷贝 QuestInfo
func (q *QuestInfo) Clone() *QuestInfo {
	if q == nil {
		return nil
	}
	return &QuestInfo{
		QuestId: q.QuestId,
		PondId:  q.PondId,
		Index:   q.Index,
	}
}

// ===== PlayerQuestData 业务方法 =====

// AddCurrentQuest 添加当前任务
func (p *PlayerQuestData) AddCurrentQuest(quest *QuestInfo) {
	p.CurrentList[quest.PondId] = append(p.CurrentList[quest.PondId], quest)
	p.UpdateTime = time.Now().Unix()
}

// AddCompletedQuest 添加完成任务
func (p *PlayerQuestData) AddCompletedQuest(quest *QuestInfo) {
	p.CompletedList = append(p.CompletedList, quest)
	p.UpdateTime = time.Now().Unix()
}

// MoveToCompleted 将任务从当前列表移到完成列表
func (p *PlayerQuestData) MoveToCompleted(questId, pondId int64) *QuestInfo {
	pondQuests, exists := p.CurrentList[pondId]
	if !exists {
		return nil
	}

	for i, quest := range pondQuests {
		if quest.QuestId == questId {
			// 从当前列表中移除
			p.CurrentList[pondId] = append(pondQuests[:i], pondQuests[i+1:]...)

			// 添加到完成列表
			quest.Index = int32(len(p.CompletedList)) // 设置完成顺序
			p.CompletedList = append(p.CompletedList, quest)
			p.UpdateTime = time.Now().Unix()

			// 返回完整的任务信息
			return &QuestInfo{
				QuestId: quest.QuestId,
				PondId:  quest.PondId,
				Index:   quest.Index,
			}
		}
	}
	return nil
}

// GetPondQuestCount 获取指定钓场的任务数量
func (p *PlayerQuestData) GetPondQuestCount(pondId int64) int {
	pondQuests, exists := p.CurrentList[pondId]
	if !exists {
		return 0
	}
	return len(pondQuests)
}
