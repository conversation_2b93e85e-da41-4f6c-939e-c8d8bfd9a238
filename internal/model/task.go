package model

import (
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/statsx"
	"time"
)

const TableTask = "t_task"

type TTask struct {
	PlayerId uint64                 `json:"player_id" xorm:"pk not null comment('玩家ID')"`
	TaskId   int64                  `json:"task_id" xorm:"pk not null comment('任务ID')"`
	Category commonPB.TASK_CATEGORY `json:"type" xorm:"comment('任务类型')"`
	SubType  int64                  `json:"sub_type" xorm:"comment('子任务类型')"`
	CondList []*statsx.Stats        `json:"cond_list" xorm:"comment('条件列表') "`
	Status   int32                  `json:"status" xorm:"comment('任务状态')"`
	Number   int32                  `json:"number" xorm:"comment('完成次数')"`
	StartTs  int64                  `json:"start_ts" xorm:"comment('开始时间')"`
	EndTs    int64                  `json:"end_ts" xorm:"comment('结束时间')"`
	UpdateAt time.Time              `json:"-" xorm:"comment('更新时间') updated"`
	// DeleteAt time.Time              `xorm:"comment('删除时间') deleted"`
}

func (q *TTask) TableName() string {
	return TableTask
}

// 实现JSONSerializable接口
func (q *TTask) FromJson(b []byte) error {
	return json.Unmarshal(b, q)
}

func (q *TTask) ToJson() []byte {
	str, _ := json.Marshal(q)
	return str
}

// GetCond 获取子条件
func (q *TTask) GetCond(key int64) *statsx.Stats {
	for _, stats := range q.CondList {
		if stats.Id == key {
			return stats
		}
	}
	return nil
}

func NewTaskByCfg(cfg *cmodel.Task) *TTask {
	return &TTask{
		TaskId:   cfg.Id,
		Category: commonPB.TASK_CATEGORY(cfg.Type),
		Status:   int32(commonPB.TASK_STATUS_TS_DURING),
	}
}

func (q *TTask) ToProto() *commonPB.TaskInfo {
	progressProto := make([]*commonPB.TaskCond, 0, len(q.CondList))
	for _, data := range q.CondList {
		progressProto = append(progressProto, &commonPB.TaskCond{
			CondId:   data.Id,
			Progress: data.Progress,
		})
	}

	return &commonPB.TaskInfo{
		TaskId:   q.TaskId,
		Progress: progressProto,
		Status:   commonPB.TASK_STATUS(q.Status),
	}
}
