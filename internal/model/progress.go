package model

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"time"
)

const TableTaskProgress = "t_task_progress"

// 进度奖励
type TTaskProgress struct {
	PlayerId uint64                 `xorm:"pk not null comment('玩家ID')"`
	Category commonPB.TASK_CATEGORY `xorm:"pk not null comment('任务类型')"`
	SubId    int64                  `xorm:"pk not null comment('子任务id')"`
	Rewarded []int64                `xorm:"comment('已奖励')"`
	Score    int64                  `xorm:"comment('分数')"`
	UpdateAt time.Time              `json:"-" xorm:"comment('更新时间') updated"`
	// DeleteAt time.Time              `json:"-" xorm:"comment('删除时间') deleted"`
}

func (t *TTaskProgress) TableName() string {
	return TableTaskProgress
}

func (t *TTaskProgress) ToProto() *commonPB.TaskProgress {
	pb := &commonPB.TaskProgress{
		Category: t.Category,
		SubId:    t.SubId,
		Rewarded: t.Rewarded,
		Score:    t.Score,
	}
	return pb
}

// 实现JSONSerializable接口（调整返回类型保持一致）
func (t *TTaskProgress) FromJson(data []byte) error {
	return json.Unmarshal(data, t)
}

func (t *TTaskProgress) ToJson() []byte {
	js, _ := json.Marshal(t)
	return js
}

func NewProgress(playerId uint64, category commonPB.TASK_CATEGORY, subId int64) *TTaskProgress {
	return &TTaskProgress{
		PlayerId: playerId,
		Category: category,
		SubId:    subId,
	}
}
