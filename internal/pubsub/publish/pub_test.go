package publish

import (
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/proto"
)

func Init() {
	viper.Set(dict.ConfigNsqDAddr, "************:4150")
	viper.Set(dict.ConfigHttpAddr, "************:4151")
	viper.Set(dict.ConfigNsqLookUpdAddress, "************:4161")
	nsqx.Setup()
}

func TestFish(t *testing.T) {
	Init()
	ProductId := int32(1)
	ChannelType := int32(1001)
	playerID := uint64(270)
	IntData := make(map[int32]int64)
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_COMMON_TS)] = timex.Now().Unix()
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)] = 5000
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_POND)] = 301020000
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_LENGTH)] = 3010
	eventType := commonPB.EVENT_TYPE_ET_FISH_GET

	pbLogin := &commonPB.EventCommon{
		PlayerId:  playerID,
		ProductId: ProductId,
		ChannelId: ChannelType,
		EventType: eventType,
		IntData:   IntData,
	}
	out, err := proto.Marshal(pbLogin)
	if err != nil {
		logrus.Errorf("proto.Marshal: %v", err)
	}
	err = mq.PublishEvent(eventType.String(), out)
	if err != nil {
		logrus.Warnf("event push %+v fail:%+v", out, err)
	}

}
