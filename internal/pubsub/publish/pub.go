package publish

import (
	"context"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"google.golang.org/protobuf/proto"
)

func PublishTaskComplete(ctx context.Context, playerID uint64, task *model.TTask) {
	entry := logx.NewLogEntry(ctx)
	opt := interceptor.GetRPCOptions(ctx)
	IntData := make(map[int32]int64)
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_COMMON_TS)] = timex.Now().Unix()
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_TASK_ID)] = task.TaskId
	IntData[int32(commonPB.EVENT_INT_KEY_EIK_TASK_SUB_TYPE)] = int64(task.SubType)

	pbLogin := &commonPB.EventCommon{
		PlayerId:  playerID,
		ProductId: opt.ProductId,
		ChannelId: opt.ChannelType,
		EventType: commonPB.EVENT_TYPE_ET_TASK_COMPLETE,
		IntData:   IntData,
	}
	out, err := proto.Marshal(pbLogin)
	if err != nil {
		entry.Errorf("proto.Marshal: %v", err)
	}
	err = mq.PublishEvent(commonPB.EVENT_TYPE_ET_TASK_COMPLETE.String(), out)
	if err != nil {
		entry.Warnf("event push %+v fail:%+v", out, err)
	}
}
