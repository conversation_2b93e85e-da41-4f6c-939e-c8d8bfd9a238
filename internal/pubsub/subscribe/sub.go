package subscribe

import (
	"context"
	"tasksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/proto"
)

func InitSubScribe() {
	// 尽量用统计服统合好的数据
	serverName := viper.GetString(dict.ConfigRpcServerName)
	// gateway
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_LOGIN.String(), serverName, HandleEvent))
	// task
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_TASK_COMPLETE.String(), serverName, HandleEvent))
	//hall
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_ADD.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_REDUCE.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ROLE_LEVEL_UP.String(), serverName, HandleEvent))
	// spot
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_FISH_GET.String(), serverName, HandleEvent))
}

// 根据配置加载事件监听器
func initPanic(err error) {
	if err != nil {
		panic(err)
	}
}

func EventPlayerCtx(event *commonPB.EventCommon) context.Context {
	return interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(event.PlayerId),
		interceptor.WithProductId(event.ProductId),
		interceptor.WithChannelType(event.ChannelId),
	)
}

// 事件管理
func HandleEvent(body []byte) {
	event := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, event); err != nil {
		logrus.Errorf("Handler CommonEvent Unmarshal fail:%s", err)
		return
	}
	logrus.Debugf("Handler CommonEvent:%+v", event)
	ctx := EventPlayerCtx(event)
	if len(event.IntData) == 0 {
		logrus.Errorf("fail data:%+v", event)
		return
	}

	// TODO 暂时固定productid = 1
	// GM 加道具获取不到productID
	if event.ProductId == 0 {
		event.ProductId = 1
	}

	services.GetTaskServiceInstance().EventPreHandle(ctx, event.PlayerId, event)
	services.GetTaskServiceInstance().EventUpdate(ctx, event.PlayerId, event)
}
