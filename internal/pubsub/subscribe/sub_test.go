package subscribe_test

import (
	"tasksrv/internal/pubsub/subscribe"
	"tasksrv/internal/services"
	"tasksrv/internal/test"
	"testing"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"google.golang.org/protobuf/proto"
)

func mockItemAdd() *commonPB.EventCommon {
	data := make(map[int32]int64)
	return &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_ITEM_ADD,
		PlayerId:  1,
		ProductId: 1,
		IntData:   data,
	}
}

func TestPush(t *testing.T) {
	test.InitNsq()
	event := mockItemAdd()
	out, err := proto.Marshal(event)
	if err != nil {
		t.Fatalf("proto marshal failed:%+v", err)
		return
	}

	err = mq.PublishEvent(event.EventType.String(), out)
	if err != nil {
		t.Fatalf("event push fail:%+v", err)
	}
	time.Sleep(time.Second * 3)
}

func TestEventUpdate(t *testing.T) {
	testx.Init()
	event := &commonPB.EventCommon{
		PlayerId:  258,
		ProductId: 1,
		ChannelId: 1001,
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		IntData: map[int32]int64{
			3001: 301020000,
			3002: 6,
			3003: 101031063,
			3004: 153,
			3006: 20,
			3007: 1,
			3008: 3,
			3009: 1,
			3010: 101020027,
			3014: 1,
			5002: 301020000,
		},
	}
	ctx := subscribe.EventPlayerCtx(event)
	services.GetTaskServiceInstance().EventUpdate(ctx, event.PlayerId, event)
}
