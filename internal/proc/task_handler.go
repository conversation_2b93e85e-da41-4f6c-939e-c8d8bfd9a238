package proc

import (
	"context"
	"tasksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"

	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

type TaskHandler struct{}

func (h *TaskHandler) GetTaskList(ctx context.Context, header *intranetGrpc.Header, req *taskPB.GetTaskListReq) *transport.ResponseMsg {
	rsp := services.GetTaskServiceInstance().GetTaskList(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TASK_GET_LIST_RSP, rsp)
}

func (h *TaskHandler) RewardTask(ctx context.Context, header *intranetGrpc.Header, req *taskPB.RewardTaskReq) *transport.ResponseMsg {
	rsp := services.GetTaskServiceInstance().RewardTask(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TASK_REWARD_RSP, rsp)
}

func (h *TaskHandler) TaskProgressReq(ctx context.Context, header *intranetGrpc.Header, req *taskPB.TaskProgressReq) *transport.ResponseMsg {
	rsp := services.GetTaskServiceInstance().TaskProgressReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TASK_PROGRESS_RSP, rsp)
}

func (h *TaskHandler) RewardTaskProgressReq(ctx context.Context, header *intranetGrpc.Header, req *taskPB.TaskProgressRewardReq) *transport.ResponseMsg {
	rsp := services.GetTaskServiceInstance().RewardTaskProgressReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_TASK_REWARD_PROGRESS_RSP, rsp)
}

func RegTaskHandler() {
	handler := &TaskHandler{}
	transport.Handler(int(commonPB.MsgID_CMD_TASK_GET_LIST_REQ), handler.GetTaskList)
	transport.Handler(int(commonPB.MsgID_CMD_TASK_REWARD_REQ), handler.RewardTask)
	transport.Handler(int(commonPB.MsgID_CMD_TASK_PROGRESS_REQ), handler.TaskProgressReq)
	transport.Handler(int(commonPB.MsgID_CMD_TASK_REWARD_PROGRESS_REQ), handler.RewardTaskProgressReq)
}
