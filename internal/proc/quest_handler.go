package proc

import (
	"context"
	"tasksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

// QuestHandler 任务处理器
type QuestHandler struct{}

// GetQuestList 获取任务列表
func (h *QuestHandler) GetQuestList(ctx context.Context, header *intranetGrpc.Header, req *taskPB.GetQuestListReq) *transport.ResponseMsg {
	rsp := services.GetQuestServiceInstance().GetQuestList(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_QUEST_LIST_RSP, rsp)
}

// SubmitQuest 提交任务
func (h *QuestHandler) SubmitQuest(ctx context.Context, header *intranetGrpc.Header, req *taskPB.SubmitQuestReq) *transport.ResponseMsg {
	rsp := services.GetQuestServiceInstance().SubmitQuest(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SUBMIT_QUEST_RSP, rsp)
}

// RefreshQuest 刷新任务
func (h *QuestHandler) RefreshQuest(ctx context.Context, header *intranetGrpc.Header, req *taskPB.RefreshQuestReq) *transport.ResponseMsg {
	rsp := services.GetQuestServiceInstance().RefreshQuest(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_REFRESH_QUEST_RSP, rsp)
}

// RegQuestHandler 注册主动任务处理器
func RegQuestHandler() {
	handler := &QuestHandler{}

	// 获取任务列表
	transport.Handler(int(commonPB.MsgID_CMD_GET_QUEST_LIST_REQ), handler.GetQuestList)

	// 提交任务
	transport.Handler(int(commonPB.MsgID_CMD_SUBMIT_QUEST_REQ), handler.SubmitQuest)

	// 刷新任务
	transport.Handler(int(commonPB.MsgID_CMD_REFRESH_QUEST_REQ), handler.RefreshQuest)
}
