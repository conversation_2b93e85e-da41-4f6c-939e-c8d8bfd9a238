package dao_task

import (
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"
	"tasksrv/internal/test"
	"testing"

	"github.com/sirupsen/logrus"
	"xorm.io/core"
)

func TestSyncTable(t *testing.T) {
	test.Init()
	engine, errGetSession := mysql_mgr.GetSqlEngine()
	if errGetSession != nil {
		return
	}

	if err := engine.Sync2(new(model.TTask)); err != nil {
		logrus.Fatalf("同步表 %s 结构失败", engine.TableName(t, true))
		return
	} else {
		logrus.Infof("同步表 %v 结构成功", t)
	}
}

func TestOrmGet(t *testing.T) {
	test.Init()
	engine, _ := mysql_mgr.GetSqlEngine()

	engine.Logger().ShowSQL(true)
	engine.Logger().SetLevel(core.LOG_INFO)
	engine.SetLogger(engine.Logger())
	session := engine.NewSession()

	var retArr []*model.TTask
	query := &model.TTask{PlayerId: 262}
	has, err := session.Exist(query)
	_ = has

	if err != nil {
		t.Fatalf("err :%+v", err)
	}
	t.Logf("info:%v", retArr)
}
