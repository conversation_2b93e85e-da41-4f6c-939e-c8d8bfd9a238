package dao_task

import (
	"context"
	"errors"
	"fmt"
	"tasksrv/config"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// redisKey
func redisKey(playerId uint64, category commonPB.TASK_CATEGORY) string {
	return fmt.Sprintf(config.RedisKeyCacheTask, playerId, category)
}


// redis client
func redisCli() *redisx.Client {
	return redisx.GetTaskCli()
}

// 获取缓存
func getCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*model.TTask, error) {
	key := redisKey(playerId, category)
	hashMap, err := redisx.GetTaskCli().HGetAllWithNil(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return nil, redisx.Empty
	}
	if err != nil {
		return nil, err
	}
	rtn := make([]*model.TTask, 0)
	for _, value := range hashMap {
		taskInfo := &model.TTask{}
		err := taskInfo.FromJson([]byte(value))
		if err != nil {
			return nil, err
		}
		rtn = append(rtn, taskInfo)
	}
	return rtn, nil
}

// 设置缓存
func setCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY, taskInfo []*model.TTask) error {
	key := redisKey(playerId, category)
	// 组装数据
	args := make([]interface{}, 0)
	for _, value := range taskInfo {
		str := value.ToJson()
		args = append(args, value.TaskId, str)
	}

	pipe := redisx.GetTaskCli().Pipeline()
	pipe.HSet(ctx, key, args...)
	pipe.Expire(ctx, key, config.TaskCacheExpire)
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

// 删除缓存
func delCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) error {
	key := redisKey(playerId, category)
	return redisx.GetTaskCli().Del(ctx, key).Err()
}
