package dao_task

import (
	"context"
	"errors"
	"tasksrv/internal/model"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/ldy105cn/xorm"
)

// 指定查询
func Get(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY, taskId int64) (*model.TTask, error) {
	redisKey := redisKey(playerId, category)
	redisCli := redisCli()
	hash, err := redisCli.HGetWithNil(ctx, redisKey, transform.Int642Str(taskId)).Result()
	if errors.Is(err, redis.Nil) {
		// 加载缓存
		arr, err := cacheDownGrade(ctx, playerId, category)
		if err != nil {
			return nil, err
		}
		for _, v := range arr {
			if v.TaskId == taskId {
				return v, nil
			}
		}
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_NOT_EXIST)
	}
	// 空标记拦截
	if errors.Is(err, redisx.Empty) {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_NOT_EXIST)
	}

	if err != nil {
		return nil, err
	}

	taskInfo := &model.TTask{}
	err = taskInfo.FromJson([]byte(hash))
	if err != nil {
		return nil, err
	}
	return taskInfo, nil
}

// 根据类型获取
func GetCategory(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*model.TTask, error) {
	cacheArr, err := getCache(ctx, playerId, category)
	// 空标记拦截
	if errors.Is(err, redisx.Empty) {
		return []*model.TTask{}, nil
	}
	if err != nil {
		return nil, err
	}
	if len(cacheArr) > 0 {
		return cacheArr, nil
	}

	return cacheDownGrade(ctx, playerId, category)

}

// GetALL 获取所有任务
func GetAll(ctx context.Context, playerId uint64) ([]*model.TTask, error) {
	// 查询所有种类

	list := make([]*model.TTask, 0)
	for _, category := range commonPB.TASK_CATEGORY_value {
		if category == 0 {
			continue
		}
		arr, err := GetCategory(ctx, playerId, commonPB.TASK_CATEGORY(category))
		if err != nil {
			return nil, err
		}
		// 拼接数据
		if len(arr) == 0 {
			continue
		}
		list = append(list, arr...)
	}

	return list, nil
}

// Modify修改任务信息
func Modify(ctx context.Context, session *xorm.Session, playerId uint64, category commonPB.TASK_CATEGORY, arr []*model.TTask) error {
	var err error
	if len(arr) == 0 {
		return protox.PB2Error(commonPB.ErrCode_ERR_BAD_PARAM)
	}
	// 更外层 锁 分布式锁
	err = delCache(ctx, playerId, category)
	if err != nil {
		return err
	}
	// 延迟双删
	time.AfterFunc(3*time.Second, func() {
		ctx := context.Background()
		delCache(ctx, playerId, category)
	})

	// 数据库操作
	err = store(session, playerId, arr)
	if err != nil {
		return err
	}

	return nil
}

// 自动分类更新
func ModifyAuto(ctx context.Context, session *xorm.Session, playerId uint64, arr ...*model.TTask) error {
	categoryMap := make(map[commonPB.TASK_CATEGORY][]*model.TTask)
	for _, one := range arr {
		category := one.Category
		if categoryMap[category] == nil {
			categoryMap[category] = make([]*model.TTask, 0)
		}
		categoryMap[category] = append(categoryMap[category], one)
	}

	for category, list := range categoryMap {
		err := Modify(ctx, session, playerId, category, list)
		if err != nil {
			return err
		}
	}

	return nil
}

// 缓存降级
func cacheDownGrade(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*model.TTask, error) {
	// 数据库操作 cache Aside
	t := &model.TTask{
		PlayerId: playerId,
		Category:     category,
	}
	arrFromDB, errGetDB := find(playerId, t)
	if errGetDB != nil {
		return nil, errGetDB
	}
	if len(arrFromDB) > 0 {
		// 刷新缓存
		_ = setCache(ctx, playerId, category, arrFromDB)
	} else {
		// 空值防击穿
		key := redisKey(playerId, category)
		redisx.GetTaskCli().HSetNil(ctx, key)
	}
	return arrFromDB, nil
}
