package dao_task

import (
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/ldy105cn/xorm"
)

// 持久化
func store(session *xorm.Session, playerID uint64, tArr []*model.TTask) error {
	if len(tArr) == 0 {
		return nil
	}

	tableSession := session.Table(model.TableTask)
	// var effected int64
	var errDone error
	for _, t := range tArr {
		model := &model.TTask{
			PlayerId: playerID,
			TaskId:   t.TaskId,
		}

		t.PlayerId = playerID
		isExist, errGet := session.Exist(model)
		if errGet != nil {
			break
		}

		if isExist {
			if t.Status == int32(commonPB.TASK_STATUS_TS_DELETE) {
				_, errDone = session.Delete(model)
				// logrus.Debugf("Influence Delete row : %d effect:%+v", effected, t)
			} else {
				_, errDone = tableSession.Where("player_id = ? AND task_id = ?", playerID, t.TaskId).Update(t)
				// logrus.Debugf("Influence update row : %d effect:%+v", effected, t)
			}
		} else {
			_, errDone = tableSession.InsertOne(t)
			// logrus.Debugf("Influence insert row : %d effect:%+v", effected, t)
		}

		if errDone != nil {
			return errDone
		}
	}

	return nil
}

// Find 根据查询类型获取
func find(playerId uint64, queryT *model.TTask) ([]*model.TTask, error) {
	engine, errGetSession := mysql_mgr.GetSqlEngine()
	if errGetSession != nil {
		return nil, errGetSession
	}

	var retArr []*model.TTask

	session := engine.Table(queryT.TableName())
	var err error
	err = session.Find(&retArr, queryT)

	// if queryT.TaskId == 0 {
	// 	// 查询用户所有任务
	// 	err = session.Where("player_id = ?", playerId).Find(&retArr)
	// } else {
	// 	err = session.Where("player_id = ? ", playerId).
	// 		Where("task_id = ?", queryT.TaskId).
	// 		Find(&retArr)
	// }

	if err != nil {
		return nil, err
	}
	return retArr, nil
}
