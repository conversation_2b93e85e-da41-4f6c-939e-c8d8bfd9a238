package task_progress

import (
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"

	"github.com/ldy105cn/xorm"
)

func store(session *xorm.Session, playerID uint64, tArr []*model.TTaskProgress) error {
	if len(tArr) == 0 {
		return nil
	}

	tableSession := session.Table(tArr[0].TableName())
	// var effected int64
	var errDone error
	for _, t := range tArr {
		model := &model.TTaskProgress{
			PlayerId: playerID,
			Category: t.Category,
			SubId:    t.SubId,
		}

		t.PlayerId = playerID
		isExist, errGet := session.Get(model)
		if errGet != nil {
			break
		}

		if isExist {
			{
				_, errDone = tableSession.Update(t, model)
				// logrus.Infof("Influence update row:%d effect:%+v", effected, t)
			}
		} else {
			_, errDone = tableSession.InsertOne(t)
			// logrus.Infof("Influence insert row:%d effect:%+v", effected, t)
		}

		if errDone != nil {
			return errDone
		}
	}

	return nil
}

// Find 根据查询类型获取
func find(playerId uint64, queryT *model.TTaskProgress) ([]*model.TTaskProgress, error) {
	engine, errGetSession := mysql_mgr.GetSqlEngine()
	if errGetSession != nil {
		return nil, errGetSession
	}

	var retArr []*model.TTaskProgress

	session := engine.Table(queryT.TableName())

	var err error
	// 查询用户所有任务
	err = session.Where("player_id = ?", playerId).Find(&retArr, queryT)

	if err != nil {
		return nil, err
	}
	return retArr, nil
}
