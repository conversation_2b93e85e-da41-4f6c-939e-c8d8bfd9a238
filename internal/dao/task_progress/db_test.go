package task_progress

import (
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"
	"tasksrv/internal/test"
	"testing"

	"github.com/sirupsen/logrus"
)

func TestSyncTable(t *testing.T) {
	test.Init()
	engine, errGetSession := mysql_mgr.GetSqlEngine()
	if errGetSession != nil {
		return
	}

	tb := &model.TTaskProgress{}
	if err := engine.Sync2(tb); err != nil {
		logrus.Fatalf("同步表 %s 结构失败", engine.TableName(t, true))
		return
	} else {
		logrus.Infof("同步表 %v 结构成功", t)
	}
}
