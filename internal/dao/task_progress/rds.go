package task_progress

import (
	"context"
	"errors"
	"fmt"
	"tasksrv/config"
	"tasksrv/internal/model"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// rediskey
func redisKey(playerId uint64, category commonPB.TASK_CATEGORY) string {
	return fmt.Sprintf(config.RedisKeyCacheTaskProgress, playerId, category)
}

// 获取所有缓存
func getCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) ([]*model.TTaskProgress, error) {
	key := redisKey(playerId, category)
	hashMap, err := redisx.GetTaskCli().HGetAllWithNil(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		return []*model.TTaskProgress{}, nil
	}
	if err != nil {
		return nil, err
	}
	rtn := make([]*model.TTaskProgress, 0)
	for _, value := range hashMap {
		taskInfo := &model.TTaskProgress{}
		err := taskInfo.FromJson([]byte(value))
		if err != nil {
			return nil, err
		}
		rtn = append(rtn, taskInfo)
	}
	return rtn, nil
}

// 设置缓存
func setCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY, taskInfo []*model.TTaskProgress) error {
	key := redisKey(playerId, category)
	// 组装数据
	args := make([]interface{}, 0)
	for _, value := range taskInfo {
		str := value.ToJson()
		args = append(args, value.Category, str)
	}

	err := redisx.GetTaskCli().HSet(ctx, key, args...).Err()
	if err != nil {
		return err
	}
	return nil
}

func delCache(ctx context.Context, playerId uint64, category commonPB.TASK_CATEGORY) error {
	key := redisKey(playerId, category)
	return redisx.GetTaskCli().Del(ctx, key).Err()
}
