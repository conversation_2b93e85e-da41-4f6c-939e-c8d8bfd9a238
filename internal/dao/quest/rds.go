package dao_quest

import (
	"context"
	"errors"
	"tasksrv/config"
	"tasksrv/internal/model"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
)

// redisKey 生成quest的Redis Key
func redisKey(questMode commonPB.QuestMode, playerId uint64, date string) string {
	return model.GetQuestRedisKey(questMode, playerId, date)
}

// redisCli 获取Redis客户端
func redisCli() *redisx.Client {
	return redisx.GetTaskCli()
}

// GetPlayerQuestData 获取玩家任务数据
func GetPlayerQuestData(ctx context.Context, questMode commonPB.QuestMode, playerId uint64, date string) (*model.PlayerQuestData, error) {
	key := redisKey(questMode, playerId, date)

	data, err := redisCli().Get(ctx, key).Result()
	if errors.Is(err, redis.Nil) {
		// 数据不存在，使用model层构建方法返回空结构
		return model.NewPlayerQuestData(), nil
	}
	if err != nil {
		return nil, err
	}

	questData := &model.PlayerQuestData{}
	err = questData.FromJson([]byte(data))
	if err != nil {
		return nil, err
	}

	return questData, nil
}

// SavePlayerQuestData 保存玩家任务数据
func SavePlayerQuestData(ctx context.Context, questMode commonPB.QuestMode, playerId uint64, date string, data *model.PlayerQuestData) error {
	key := redisKey(questMode, playerId, date)

	data.UpdateTime = time.Now().Unix()
	jsonData := data.ToJson()

	err := redisCli().Set(ctx, key, jsonData, config.QuestCacheExpire).Err()
	if err != nil {
		return err
	}

	return nil
}
