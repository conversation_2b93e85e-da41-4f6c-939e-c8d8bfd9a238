package test

import (
	"context"
	"fmt"
	"path"
	"runtime"
	"strings"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init() {
	InitLog()
	InitRedis()
	InitSql()
	InitConsul()
}

func NewCtxWithPlayerId(playerId uint64) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(1),
		interceptor.WithChannelType(1001),
		interceptor.WithCountry("zh-cn"),
		interceptor.WithPlayerId(playerId),
	)
	return ctx
}

const addr = "************"

func InitRedis() {

	passwd := "8888"
	conf := map[string]string{
		"addr":   addr + ":6379",
		"passwd": "8888",
	}
	viper.Set(dict.ConfigRedisAddr, addr)
	viper.Set(dict.ConfigRedisPwd, passwd)
	viper.Set("redis_list", map[string]interface{}{
		dict_redis.RDBTask: conf,
		dict_redis.RDBLock: conf,
	})
}

func InitConsul() {
	viper.SetDefault(dict.ConfigConsulAddr, addr+":8500")
}

func InitLog() {
	logrus.SetLevel(logrus.DebugLevel)
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableTimestamp: false,
		// TimestampFormat:  "2006-01-02 15:04:05",
		TimestampFormat: time.RFC3339Nano,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			s := strings.Split(f.Function, ".")
			funcName := s[len(s)-1]
			filepath, filename := path.Split(f.File)
			return funcName, filepath + filename + fmt.Sprintf(":%d", f.Line)
		},
		PrettyPrint: false,
	})
}

func InitSql() {
	conf := map[string]interface{}{
		"addr": "************:3306",
		// "addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBTask,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBTask: conf,
	})
}

func InitNsq() {
	viper.SetDefault(dict.ConfigNsqDAddr, "localhost:4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, "localhost:4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, "localhost:4161")
	nsqx.Setup()
}
