package mysql_mgr

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/kit/mysql"
	"github.com/ldy105cn/xorm"
)

func GetSqlEngine() (*xorm.Engine, error) {
	engine, err := mysql.GetDefaultMgr().GetMyEngine(dict_mysql.MysqlDBTask)
	if err != nil {
		return nil, err
	}
	// engine.Logger().SetLevel(core.LOG_DEBUG)
	// engine.ShowSQL(true)

	return engine, nil
}
