package rpcSpot

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/spotrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_spot"
)

// PondEventChangeNtf 钓场变化事件通知
func PondEventChangeNtf(ctx context.Context, playerId uint64, roomInfo *commonPB.RoomInfo, eventList []*commonPB.PondEventChangeInfo) error {
	entry := logx.NewLogEntry(ctx)
	if roomInfo == nil || len(eventList) <= 0 {
		entry.Warnf("send player:%d, roomInfo:%+v, event:%+v change nft invalid", playerId, roomInfo, eventList)
		return fmt.Errorf("roomInfo is nil or eventList is empty")
	}

	ntfBody := &spotRpc.PondChangeEventNtf{
		PlayerId:  playerId,
		RoomInfo:  roomInfo,
		EventList: eventList,
	}

	spotRpcCli := crpc_spot.GetSpotRpcInstance().GetSpotRpcClient()
	if spotRpcCli == nil {
		entry.Errorf("send player:%d pond event change nft error, spot rpc client is nil", playerId)
		return fmt.Errorf("spot rpc client is nil")
	}

	_, err := spotRpcCli.PondChangeEventNotify(ctx, ntfBody)

	entry.Debugf("send player:%d pond event change nft:%s ret:%v", playerId, ntfBody.String(), err)

	return err
}

// GetKeepnetFishInfo 获取鱼护详细信息
func GetKeepnetFishInfo(ctx context.Context, playerId uint64) (*spotRpc.KeepnetFishInfoRsp, error) {
	entry := logx.NewLogEntry(ctx)

	spotRpcCli := crpc_spot.GetSpotRpcInstance().GetSpotRpcClient()
	if spotRpcCli == nil {
		entry.Errorf("send player:%d pond event change nft error, spot rpc client is nil", playerId)
		return nil, fmt.Errorf("spot rpc client is nil")
	}

	rsp, err := spotRpcCli.GetKeepnetFishInfo(ctx, &spotRpc.KeepnetFishInfoReq{})

	entry.Debugf("send player:%d pond event change rsp:%+v err:%v", playerId, rsp, err)

	return rsp, err
}

// BatchOperateFish 批量操作鱼护
func BatchOperateFish(ctx context.Context, pondId int64, operateType commonPB.FISH_KEEPNET_OPT_TYPE, fishIdList []string) (*spotRpc.FishKeepnetBatchOptRsp, error) {
	entry := logx.NewLogEntry(ctx)
	if len(fishIdList) <= 0 {
		entry.Warnf("fishIdList is empty")
		return nil, fmt.Errorf("fishIdList is empty")
	}

	req := &spotRpc.FishKeepnetBatchOptReq{
		PondId:        pondId,
		FishInstances: fishIdList,
		Action:        operateType,
	}

	spotRpcCli := crpc_spot.GetSpotRpcInstance().GetSpotRpcClient()
	if spotRpcCli == nil {
		entry.Errorf("spot rpc client is nil")
		return nil, fmt.Errorf("spot rpc client is nil")
	}

	rsp, err := spotRpcCli.OperateKeepnetFish(ctx, req)

	entry.Debugf("operate keepnet fish rsp:%+v err:%v", rsp, err)

	return rsp, err
}
