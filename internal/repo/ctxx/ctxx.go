package ctxx

import (
	"context"
	"tasksrv/config"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	m_user "git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_model/rpc_model_user"
)

func GetUserInfo(ctx context.Context, playerId uint64) (context.Context, *commonPB.BriefUserInfo, error) {
	data := ctx.Value(config.KEY_CTX_USER_INFO)
	if data != nil {
		userInfo, ok := data.(*commonPB.BriefUserInfo)
		if ok {
			return ctx, userInfo, nil
		}
	}
	userInfo, err := m_user.GetUserInfoMgrInstance().GetBriefUser(playerId)
	if err != nil {
		return ctx, nil, err
	}
	newCtx := context.WithValue(ctx, config.KEY_CTX_USER_INFO, userInfo)

	return newCtx, userInfo, nil
}

func GetRoleLev(ctx context.Context, playerId uint64) (context.Context, int64, error) {
	// FIXME:
	return ctx, 10, nil
}
