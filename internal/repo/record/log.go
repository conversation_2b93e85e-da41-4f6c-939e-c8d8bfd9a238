package record

import (
	"context"
	"sync"
	"tasksrv/internal/model"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
)

var (
	producer     event.Sender
	producerOnce sync.Once
)

// InitProducer 初始化消息生产者，确保只初始化一次
func InitProducer() {
	producerOnce.Do(func() {
		sender, err := event.NewKafkaSender()
		if err != nil {
			logrus.Fatalf("创建Kafka发送器失败: %s", err)
		}
		producer = sender
	})
}

// GetProducer 返回初始化后的消息生产者实例
func GetProducer() event.Sender {
	return producer
}

func PubTaskRecord(ctx context.Context, playerId uint64, changeList []*model.TTask) {
	header := recordx.NewDefaultHeaderFromCtx(ctx)
	for _, change := range changeList {
		record := &RTask{
			PlayerId:   playerId,
			TaskId:     change.TaskId,
			TaskStatus: int64(change.Status),
		}
		body := recordx.SerializeData(header, record)
		GetProducer().SendWithTopic(ctx, record.GetTableName(), event.NewMessage(cast.ToString(playerId), []byte(body)))
	}
}

func PubQuestRecord(ctx context.Context, hookContext *model.QuestHookContext) {
	header := recordx.NewDefaultHeaderFromCtx(ctx)
	record := &RQuest{
		PlayerId:        hookContext.PlayerId,
		QuestId:         hookContext.QuestId,
		QuestMode:       int64(hookContext.QuestMode),
		SelectedFishIds: hookContext.SelectedFishIds,
		QuestCond:       protox.ToJson(hookContext.QuestCond),
		QuestReward:     protox.ToJson(hookContext.QuestCond),
	}
	body := recordx.SerializeData(header, record)

	GetProducer().SendWithTopic(ctx, record.GetTableName(), event.NewMessage(cast.ToString(hookContext.PlayerId), []byte(body)))
}
