package record

import (
	"tasksrv/internal/model"
	"testing"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/spf13/viper"
)

func TestLog(t *testing.T) {
	testx.Init()
	viper.Set("kafka-producer.timeout", 10)
	viper.Set("kafka-producer.brokers", []string{"************:9092"})

	InitProducer()
	time.Sleep(1 * time.Second)
	PubTaskRecord(testx.TestCtx(1, 1), 1, []*model.TTask{
		{
			PlayerId: 1,
			TaskId:   1,
			Status:   5,
		},
	})
	time.Sleep(1 * time.Second)
}
