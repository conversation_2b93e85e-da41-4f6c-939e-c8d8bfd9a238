package record

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type RTask struct {
	PlayerId   uint64 // 玩家ID
	TaskId     int64  // 任务ID
	TaskStatus int64  // 任务状态 1:领取 2：完成
}

func (r *RTask) GetTableName() string {
	return "r_task"
}

func (r *RTask) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		transform.Int642Str(r.TaskId),
		transform.Int642Str(r.TaskStatus),
	)
}
