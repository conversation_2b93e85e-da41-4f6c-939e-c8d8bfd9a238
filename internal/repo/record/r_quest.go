package record

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type RQuest struct {
	PlayerId        uint64   // 玩家ID
	QuestId         int64    // 任务ID
	QuestMode       int64    // 任务模式
	SelectedFishIds []string // 选中的鱼ID列表
	QuestCond       string   // 任务条件
	QuestReward     string   // 任务奖励

}

func (r *RQuest) GetTableName() string {
	return "r_quest"
}

func (r *RQuest) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		transform.Int642Str(r.QuestId),
		transform.Int642Str(r.QuestMode),
		transform.Slice2Str(r.SelectedFishIds, ","),
		r.QuestCond,
		r.QuestReward,
	)
}
