package services

import (
	"context"
	"sync"
	"tasksrv/config"
	dao_task "tasksrv/internal/dao/task"
	"tasksrv/internal/logic/logic_event"
	"tasksrv/internal/logic/logic_task"
	"tasksrv/internal/logic/logic_task_progress"
	"tasksrv/internal/logic/push"
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

type TaskService struct {
}

var (
	taskOnce              = &sync.Once{}
	taskSingletonInstance *TaskService
)

func GetTaskServiceInstance() *TaskService {
	if taskSingletonInstance != nil {
		return taskSingletonInstance
	}

	taskOnce.Do(func() {
		taskSingletonInstance = &TaskService{}
		taskSingletonInstance.Init()
	})
	return taskSingletonInstance
}

func (s *TaskService) Init() {
}

// GetTaskList 获取任务列表
func (s *TaskService) GetTaskList(ctx context.Context, req *taskPB.GetTaskListReq) *taskPB.GetTaskListRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("GetTaskList req:%v", req)
	rpcRsp := &taskPB.GetTaskListRsp{Ret: protox.DefaultResult()}
	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	// 重新获取全量信息
	accepted, err := dao_task.GetCategory(ctx, playerId, req.GetCategory())
	if err != nil {
		rpcRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_DB_OPE_ERROR, err.Error())
		return rpcRsp
	}
	taskList := filterPushTask(req.Category, accepted)

	rpcRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	// 过滤任务列表 推送
	rpcRsp.TaskList = taskList
	rpcRsp.Category = req.GetCategory()

	return rpcRsp
}

// 过滤需要推送的任务
func filterPushTask(category commonPB.TASK_CATEGORY, taskList []*model.TTask) (new []*commonPB.TaskInfo) {
	new = make([]*commonPB.TaskInfo, 0)
	for _, task := range taskList {
		// 刚登录只需要关注正在执行的任务
		// switch category {
		// case commonPB.TASK_CATEGORY_TC_ACHIEVE:
		// 	new = append(new, task.ToProto())
		// default:
		// 	if task.Status == int32(commonPB.TASK_STATUS_TS_DURING) {
		// 		new = append(new, task.ToProto())
		// 	}

		new = append(new, task.ToProto())
	}
	return
}

// RewardTask 领取奖励
func (s *TaskService) RewardTask(ctx context.Context, req *taskPB.RewardTaskReq) *taskPB.RewardTaskRsp {
	rpcRsp := &taskPB.RewardTaskRsp{Ret: protox.DefaultResult()}
	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	rsp, err := logic_task.Reward(ctx, playerId, req.GetCategory(), req.GetTaskId())
	if err != nil {
		rpcRsp.Ret = protox.FillErrResult(err)
		return rpcRsp
	}

	// return ok
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	return rsp
}

// EventPreHandle 事件系统预处理
func (s *TaskService) EventPreHandle(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	switch event.EventType {
	// 需要预处理事件
	case commonPB.EVENT_TYPE_ET_LOGIN, commonPB.EVENT_TYPE_ET_TASK_COMPLETE, commonPB.EVENT_TYPE_ET_ROLE_LEVEL_UP:
		entry := logx.NewLogEntry(ctx)
		entry.Debugf("eventPreHandle start")

		// 交互锁
		unlock := dlm.LockKey(config.DLMTaskLockKey(playerId))
		defer unlock()

		change, err := logic_task.TaskAccept(ctx, playerId)
		if err != nil {
			entry.Errorf("eventPreHandle fail:%+v", err)
			return
		}

		// 推送变化
		push.TaskUpdateNTF(ctx, playerId, change...)

		entry.Debugf("eventPreHandle success")
	default:
		return
	}
}

// 事件触发更新
func (s *TaskService) EventUpdate(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	entry := logx.NewLogEntry(ctx)

	// 交互锁
	unlock := dlm.LockKey(config.DLMTaskLockKey(playerId))
	defer unlock()

	changeList, err := logic_event.EventChangeCheck(ctx, playerId, event)
	if err != nil {
		entry.Errorf("event update fail:%+v event:%+v", err, event)
		return
	}

	if len(changeList) > 0 {
		engine, err := mysql_mgr.GetSqlEngine()
		if err != nil {
			// 数据库获取失败
			entry.Warnf("session get error:%v", err)
			return
		}

		session := engine.NewSession()
		defer session.Close()

		_ = session.Begin()

		err = dao_task.ModifyAuto(ctx, session, playerId, changeList...)
		if err != nil {
			errRollBack := session.Rollback()
			if errRollBack != nil {
				entry.Warnf("session rollback error:%v", err)
				return
			}
			// 更新状态失败
			entry.Warnf("session update error:%v", err)
			return
		}
		err = session.Commit()
		if err != nil {
			// 数据库获取失败
			entry.Warnf("session commit error:%v", err)
			return
		}

		// 推送下发
		push.TaskUpdateNTF(ctx, playerId, changeList...)
	}
}

// 请求任务进度数据
func (s *TaskService) TaskProgressReq(ctx context.Context, req *taskPB.TaskProgressReq) *taskPB.TaskProgressRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("TaskProgressReq req:%v", req)
	rpcRsp := &taskPB.TaskProgressRsp{Ret: protox.DefaultResult()}

	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId
	// 获取已领奖信息
	// 获取/计算积分
	list, err := logic_task_progress.GetTaskProgress(ctx, playerId, req.GetCategory())
	if err != nil {
		// TODO 修改错误码
		rpcRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_UNKNOWN, err.Error())
		return rpcRsp
	}
	rpcRsp.Category = req.GetCategory()
	rpcRsp.List = list
	rpcRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rpcRsp
}

func (s *TaskService) RewardTaskProgressReq(ctx context.Context, req *taskPB.TaskProgressRewardReq) *taskPB.TaskProgressRewardRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("RewardTaskPondReq req:%v", req)
	rpcRsp := &taskPB.TaskProgressRewardRsp{Ret: protox.DefaultResult()}

	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	vo, err := logic_task_progress.RewardProgress(ctx, playerId, req.GetCategory(), req.GetSubId(), req.GetIndex())
	if err != nil {
		rpcRsp.Ret = protox.FillErrResult(err)
		return rpcRsp
	}

	rpcRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rpcRsp.Reward = vo.Reward
	rpcRsp.Info = vo.TTaskProgress.ToProto()

	return rpcRsp
}
