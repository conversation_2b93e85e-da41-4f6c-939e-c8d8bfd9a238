package services

import (
	"context"
	"sync"
	"tasksrv/internal/logic/logic_quest"

	taskPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

// QuestService 任务服务
type QuestService struct{}

var (
	questOnce              = &sync.Once{}
	questSingletonInstance *QuestService
)

// GetQuestServiceInstance 获取任务服务单例
func GetQuestServiceInstance() *QuestService {
	if questSingletonInstance != nil {
		return questSingletonInstance
	}

	questOnce.Do(func() {
		questSingletonInstance = &QuestService{}
		questSingletonInstance.Init()
	})
	return questSingletonInstance
}

// Init 初始化服务
func (s *QuestService) Init() {}

// GetQuestList 获取任务列表
func (s *QuestService) GetQuestList(ctx context.Context, req *taskPB.GetQuestListReq) *taskPB.GetQuestListRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("GetQuestList req:%+v", req)

	rpcRsp := &taskPB.GetQuestListRsp{Ret: protox.DefaultResult()}
	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	rsp, err := logic_quest.GetQuestList(ctx, playerId, req)
	if err != nil {
		entry.Errorf("get quest list failed: %+v", err)
		rpcRsp.Ret = protox.FillErrResult(err)
		return rpcRsp
	}
	entry.Infof("GetQuestList rsp:%+v", rsp)
	return rsp
}

// SubmitQuest 提交任务
func (s *QuestService) SubmitQuest(ctx context.Context, req *taskPB.SubmitQuestReq) *taskPB.SubmitQuestRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("SubmitQuest req:%+v", req)

	rpcRsp := &taskPB.SubmitQuestRsp{Ret: protox.DefaultResult()}
	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	rsp, err := logic_quest.SubmitQuest(ctx, playerId, req)
	if err != nil {
		entry.Errorf("submit quest failed: %+v", err)
		rpcRsp.Ret = protox.FillErrResult(err)
		return rpcRsp
	}
	entry.Infof("SubmitQuest rsp:%+v", rsp)
	return rsp
}

// RefreshQuest 刷新任务
func (s *QuestService) RefreshQuest(ctx context.Context, req *taskPB.RefreshQuestReq) *taskPB.RefreshQuestRsp {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("RefreshQuest req:%+v", req)

	rpcRsp := &taskPB.RefreshQuestRsp{Ret: protox.DefaultResult()}
	opt := interceptor.GetRPCOptions(ctx)
	playerId := opt.PlayerId

	rsp, err := logic_quest.RefreshQuest(ctx, playerId, req)
	if err != nil {
		entry.Errorf("refresh quest failed: %+v", err)
		rpcRsp.Ret = protox.FillErrResult(err)
		return rpcRsp
	}
	entry.Infof("RefreshQuest rsp:%+v", rsp)
	return rsp
}
