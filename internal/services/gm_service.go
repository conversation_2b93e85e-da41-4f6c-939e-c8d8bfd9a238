package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	dao_task "tasksrv/internal/dao/task"
	dao_task_progress "tasksrv/internal/dao/task_progress"
	"tasksrv/internal/logic/logic_task"
	"tasksrv/internal/logic/push"
	"tasksrv/internal/model"
	"tasksrv/internal/repo/mysql_mgr"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/statsx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"github.com/ldy105cn/xorm"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
)

type GmService struct{}

var (
	gmOnce              = &sync.Once{}
	gmSingletonInstance *GmService
)

func GetGmInstance() *GmService {
	if gmSingletonInstance != nil {
		return gmSingletonInstance
	}
	gmOnce.Do(func() {
		gmSingletonInstance = &GmService{}
	})
	return gmSingletonInstance
}

func (s *GmService) OperateTask(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:OperateTask]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdOperateTaskReq{}
	err := protojson.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}

	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		return nil, err
	}
	session := engine.NewSession()
	defer session.Close()
	session.Begin()
	switch reqd.Operate {
	case gmPB.GM_TASK_OPERATE_GM_TASK_OPERATE_SET: // 强制添加任务
		if err := addTask(ctx, reqd.PlayerId, session, reqd.TaskId); err != nil {
			session.Rollback()
			return nil, err
		}
	case gmPB.GM_TASK_OPERATE_GM_TASK_OPERATE_FINISH: // 强制完成任务
		if err := finishTask(ctx, reqd.PlayerId, session, reqd.TaskId); err != nil {
			session.Rollback()
			return nil, err
		}
	case gmPB.GM_TASK_OPERATE_GM_TASK_OPERATE_DELETE: // 强制删除任务
		if err := delTask(ctx, reqd.PlayerId, session, reqd.TaskId); err != nil {
			session.Rollback()
			return nil, err
		}
	}
	session.Commit()
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}

// 增加任务
func addTask(ctx context.Context, playerId uint64, seesion *xorm.Session, taskId int64) error {
	var err error
	taskCfg := cmodel.GetTask(taskId, consul_config.WithGrpcCtx(ctx))
	if taskCfg == nil {
		return fmt.Errorf("taskid[%d] not found", taskId)
	}

	newTask := model.NewTaskByCfg(taskCfg)

	ctx, err = logic_task.FlushInitTask(ctx, newTask)
	if err != nil {
		return err
	}

	err = dao_task.ModifyAuto(ctx, seesion, playerId, newTask)
	if err != nil {
		return err
	}

	push.TaskUpdateNTF(ctx, playerId, newTask)

	return nil
}

// 强制完成任务
func finishTask(ctx context.Context, playerId uint64, seesion *xorm.Session, taskId int64) error {
	taskCfg := cmodel.GetTask(taskId, consul_config.WithGrpcCtx(ctx))
	if taskCfg == nil {
		return fmt.Errorf("taskid[%d] not found", taskId)
	}

	newTask := model.NewTaskByCfg(taskCfg)
	newTask.PlayerId = playerId
	condGroupCfg := cmodel.GetTaskCondGroup(taskCfg.CondGroup, consul_config.WithGrpcCtx(ctx))
	if condGroupCfg == nil {
		return fmt.Errorf("task cond group id[%d] not found", taskCfg.CondGroup)
	}
	for _, condId := range condGroupCfg.Conds {
		condCfg := cmodel.GetTaskCond(condId, consul_config.WithGrpcCtx(ctx))
		if condCfg == nil {
			continue
		}
		stats := statsx.NewStats(condId)
		if newTask.CondList == nil {
			newTask.CondList = make([]*statsx.Stats, 0)
		}
		newTask.CondList = append(newTask.CondList, stats)
		stats.Progress = condCfg.Target
	}

	err := dao_task.ModifyAuto(ctx, seesion, playerId, newTask)
	if err != nil {
		return err
	}

	push.TaskUpdateNTF(ctx, playerId, newTask)

	return nil
}

func delTask(ctx context.Context, playerId uint64, seesion *xorm.Session, taskId int64) error {
	accepted, err := dao_task.GetAll(ctx, playerId)
	if err != nil {
		return err
	}
	changeList := make([]*model.TTask, 0)
	for _, task := range accepted {
		if task.TaskId == taskId || taskId == 0 {
			task.Status = int32(*commonPB.TASK_STATUS_TS_DELETE.Enum())
			err := dao_task.ModifyAuto(ctx, seesion, playerId, task)
			if err != nil {
				return err
			}

			changeList = append(changeList, task)
		}
	}
	push.TaskUpdateNTF(ctx, playerId, changeList...)

	return nil
}

func (s *GmService) TaskProgressSet(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdTaskProgressSetReq{}
	err := protojson.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}
	playerId := reqd.PlayerId

	// FIXME: 临时处理
	progress, err := dao_task_progress.GetOne(ctx, playerId, commonPB.TASK_CATEGORY(reqd.Category), reqd.GetSubId())
	if err != nil {
		logrus.Error(ctx, "get task progress error", err)
		// 如果不存在硬写
		progress = model.NewProgress(playerId, commonPB.TASK_CATEGORY(reqd.Category), reqd.GetSubId())
	}
	progress.Rewarded = make([]int64, 0)
	progress.Score = int64(reqd.Score)

	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		return nil, err
	}
	session := engine.NewSession()
	defer session.Close()
	session.Begin()

	if err := dao_task_progress.ModifyAuto(ctx, session, playerId, progress); err != nil {
		session.Rollback()
		return nil, err
	}

	session.Commit()
	push.TaskProgressNTF(ctx, playerId, progress)

	gmRsp := &gmPB.GmCmdTaskProgressSetRsp{}
	gmRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	js, _ := json.Marshal(gmRsp)

	rsp.Data = string(js)

	return rsp, nil
}

func (s *GmService) TaskSubUpdate(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:SubUpdate]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdTaskSubUpdateReq{}
	err := protojson.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}
	playerId := reqd.PlayerId

	accepted, err := dao_task.GetAll(ctx, playerId)
	if err != nil {
		return nil, err
	}
	var acceptedTask *model.TTask
	for _, task := range accepted {
		if task.TaskId == reqd.TaskId {
			acceptedTask = task
			break
		}
	}
	if acceptedTask == nil {
		return nil, fmt.Errorf("taskid[%d] not found", reqd.TaskId)
	}

	cond := acceptedTask.GetCond(reqd.SubTaskId)
	if cond == nil {
		cond = statsx.NewStats(reqd.SubTaskId)
		acceptedTask.CondList = append(acceptedTask.CondList, cond)
	}
	cond.Progress = reqd.Progress

	engine, err := mysql_mgr.GetSqlEngine()
	if err != nil {
		return nil, err
	}
	session := engine.NewSession()
	defer session.Close()
	session.Begin()

	if err := dao_task.ModifyAuto(ctx, session, playerId, acceptedTask); err != nil {
		session.Rollback()
		return nil, err
	}

	session.Commit()
	push.TaskUpdateNTF(ctx, playerId, acceptedTask)

	gmRsp := &gmPB.GmCmdTaskSubUpdateRsp{}
	// gmRsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	js, _ := json.Marshal(gmRsp)

	rsp.Data = string(js)

	return rsp, nil
}
