package rpc

import (
	"tasksrv/internal/services"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
)

type GmRpcServer struct {
}

func InitGmRpc() {
	gm_handler.InitGmHandler()

	instance := services.GetGmInstance()
	gm_handler.Handler(commonPB.GM_CMD_GM_TASK_OPERATE, instance.OperateTask)
	gm_handler.Handler(commonPB.GM_CMD_GM_TASK_PROGRESS_SET, instance.TaskProgressSet)
	gm_handler.Handler(commonPB.GM_CMD_GM_TASK_SUB_UPDATE, instance.TaskSubUpdate)
}
